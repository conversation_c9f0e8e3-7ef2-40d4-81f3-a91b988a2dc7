<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/car_theme_primary" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- 聚焦状态 -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/car_bg_tertiary" />
            <stroke android:width="2dp" android:color="@color/car_theme_primary" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/car_bg_tertiary" />
            <corners android:radius="8dp" />
        </shape>
    </item>
</selector>
