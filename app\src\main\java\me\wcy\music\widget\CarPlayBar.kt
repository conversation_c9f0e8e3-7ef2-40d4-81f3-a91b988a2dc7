package me.wcy.music.widget

import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import me.wcy.music.R
import me.wcy.music.consts.RoutePath
import me.wcy.music.databinding.LayoutCarPlayBarBinding
import me.wcy.music.main.playlist.CurrentPlaylistFragment
import me.wcy.music.service.PlayServiceModule.playerController
import me.wcy.music.service.PlayState
import me.wcy.music.utils.getDuration
import me.wcy.music.utils.getSmallCover
import me.wcy.router.CRouter
import me.wcy.music.utils.CarAnimationUtils.enableHardwareAcceleration
import me.wcy.music.utils.CarAnimationUtils.fadeIn
import me.wcy.music.utils.CarAnimationUtils.fadeOut
import me.wcy.music.utils.CarAnimationUtils.playStateAnimation
import me.wcy.music.utils.CarTouchUtils.performCarHapticFeedback
import me.wcy.music.utils.loadCarAlbumCoverSmall
import top.wangchenyan.common.CommonApp
import top.wangchenyan.common.ext.findActivity
import top.wangchenyan.common.ext.findLifecycleOwner

/**
 * 车载播放控制栏
 * Created by wangchenyan.top on 2023/12/20.
 */
class CarPlayBar @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {
    private val viewBinding: LayoutCarPlayBarBinding
    private val playerController by lazy {
        CommonApp.app.playerController()
    }
    private val rotateAnimator: ObjectAnimator

    init {
        id = R.id.play_bar
        viewBinding = LayoutCarPlayBarBinding.inflate(LayoutInflater.from(context), this, true)

        // 启用硬件加速
        enableHardwareAcceleration()

        rotateAnimator = ObjectAnimator.ofFloat(viewBinding.ivCover, "rotation", 0f, 360f).apply {
            duration = 20000
            repeatCount = ValueAnimator.INFINITE
            repeatMode = ObjectAnimator.RESTART
            interpolator = LinearInterpolator()
        }

        initView()
        context.findLifecycleOwner()?.let {
            initData(it)
        }
    }

    private fun initView() {
        viewBinding.root.setOnClickListener {
            it.performCarHapticFeedback()
            CRouter.with(context).url(RoutePath.PLAYING).start()
        }
        viewBinding.flPlay.setOnClickListener {
            it.performCarHapticFeedback()
            playerController.playPause()
        }
        viewBinding.ivPrev.setOnClickListener {
            it.performCarHapticFeedback()
            playerController.prev()
        }
        viewBinding.ivNext.setOnClickListener {
            it.performCarHapticFeedback()
            playerController.next()
        }
        viewBinding.ivPlaylist.setOnClickListener {
            it.performCarHapticFeedback()
            val activity = context.findActivity()
            if (activity is FragmentActivity) {
                CurrentPlaylistFragment.newInstance()
                    .show(activity.supportFragmentManager, CurrentPlaylistFragment.TAG)
            }
        }
    }

    private fun initData(lifecycleOwner: LifecycleOwner) {
        playerController.currentSong.observe(lifecycleOwner) { currentSong ->
            if (currentSong != null) {
                if (!isVisible) {
                    fadeIn()
                }
                viewBinding.ivCover.loadCarAlbumCoverSmall(currentSong.getSmallCover(), 8)
                viewBinding.tvTitle.text = currentSong.mediaMetadata.title
                viewBinding.tvArtist.text = currentSong.mediaMetadata.artist
                viewBinding.progressBar.max = currentSong.mediaMetadata.getDuration().toInt()
                viewBinding.progressBar.progress = playerController.playProgress.value.toInt()
            } else {
                if (isVisible) {
                    fadeOut()
                }
            }
        }

        lifecycleOwner.lifecycleScope.launch {
            playerController.playState.collectLatest { playState ->
                when (playState) {
                    PlayState.Preparing -> {
                        viewBinding.flPlay.isEnabled = false
                        viewBinding.ivPlay.isSelected = false
                        viewBinding.loadingProgress.fadeIn()
                    }

                    PlayState.Playing -> {
                        viewBinding.flPlay.isEnabled = true
                        viewBinding.ivPlay.isSelected = true
                        viewBinding.loadingProgress.fadeOut()
                        // 播放状态动画
                        viewBinding.flPlay.playStateAnimation(true)
                    }

                    else -> {
                        viewBinding.flPlay.isEnabled = true
                        viewBinding.ivPlay.isSelected = false
                        viewBinding.loadingProgress.fadeOut()
                        // 暂停状态动画
                        viewBinding.flPlay.playStateAnimation(false)
                    }
                }

                if (playState.isPlaying) {
                    if (rotateAnimator.isPaused) {
                        rotateAnimator.resume()
                    } else if (rotateAnimator.isStarted.not()) {
                        rotateAnimator.start()
                    }
                } else {
                    if (rotateAnimator.isRunning) {
                        rotateAnimator.pause()
                    }
                }
            }
        }

        lifecycleOwner.lifecycleScope.launch {
            playerController.playProgress.collectLatest {
                viewBinding.progressBar.progress = it.toInt()
            }
        }
    }
}
