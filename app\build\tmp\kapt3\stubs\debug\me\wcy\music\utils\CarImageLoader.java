package me.wcy.music.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.widget.ImageView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestBuilder;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;
import kotlinx.coroutines.Dispatchers;
import me.wcy.music.R;
import java.lang.ref.WeakReference;

/**
 * 车载环境优化的图片加载器
 * 专为Android Automotive设计，优化内存使用和加载性能
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010%\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u0001\"B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u00072\u0006\u0010\r\u001a\u00020\tH\u0002J\b\u0010\u000e\u001a\u00020\u000bH\u0002J\u000e\u0010\u000f\u001a\u00020\u000b2\u0006\u0010\u0010\u001a\u00020\u0011J\u0006\u0010\u0012\u001a\u00020\u0007J\u001a\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00042\b\b\u0002\u0010\u0016\u001a\u00020\u0004H\u0002J(\u0010\u0017\u001a\u00020\u000b2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0018\u001a\u00020\u00072\b\b\u0002\u0010\u0015\u001a\u00020\u0004H\u0086@\u00a2\u0006\u0002\u0010\u0019J.\u0010\u001a\u001a\u00020\u000b2\u0006\u0010\u0010\u001a\u00020\u00112\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00070\u001c2\b\b\u0002\u0010\u0015\u001a\u00020\u0004H\u0086@\u00a2\u0006\u0002\u0010\u001dJB\u0010\u001e\u001a\u00020\u000b*\u00020\u001f2\b\u0010\u0018\u001a\u0004\u0018\u00010\u00012\b\b\u0002\u0010\u0015\u001a\u00020\u00042\b\b\u0002\u0010\u0016\u001a\u00020\u00042\u0018\b\u0002\u0010 \u001a\u0012\u0012\u0006\u0012\u0004\u0018\u00010\t\u0012\u0004\u0012\u00020\u000b\u0018\u00010!R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R \u0010\u0005\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006#"}, d2 = {"Lme/wcy/music/utils/CarImageLoader;", "", "()V", "MAX_CACHE_SIZE", "", "bitmapCache", "", "", "Ljava/lang/ref/WeakReference;", "Landroid/graphics/Bitmap;", "cacheBitmap", "", "key", "bitmap", "cleanupCache", "clearCache", "context", "Landroid/content/Context;", "getCacheInfo", "getCarOptimizedOptions", "Lcom/bumptech/glide/request/RequestOptions;", "size", "corners", "preloadImage", "url", "(Landroid/content/Context;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "preloadImages", "urls", "", "(Landroid/content/Context;Ljava/util/List;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "loadCarCover", "Landroid/widget/ImageView;", "onLoadComplete", "Lkotlin/Function1;", "CarImageSize", "app_debug"})
public final class CarImageLoader {
    @org.jetbrains.annotations.NotNull()
    private static final java.util.Map<java.lang.String, java.lang.ref.WeakReference<android.graphics.Bitmap>> bitmapCache = null;
    private static final int MAX_CACHE_SIZE = 50;
    @org.jetbrains.annotations.NotNull()
    public static final me.wcy.music.utils.CarImageLoader INSTANCE = null;
    
    private CarImageLoader() {
        super();
    }
    
    /**
     * 为车载环境优化的图片加载配置
     */
    private final com.bumptech.glide.request.RequestOptions getCarOptimizedOptions(int size, int corners) {
        return null;
    }
    
    /**
     * 加载专辑封面 - 车载优化版本
     */
    public final void loadCarCover(@org.jetbrains.annotations.NotNull()
    android.widget.ImageView $this$loadCarCover, @org.jetbrains.annotations.Nullable()
    java.lang.Object url, int size, int corners, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super android.graphics.Bitmap, kotlin.Unit> onLoadComplete) {
    }
    
    /**
     * 预加载图片
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object preloadImage(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String url, int size, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 批量预加载图片
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object preloadImages(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> urls, int size, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 缓存Bitmap到内存
     */
    private final void cacheBitmap(java.lang.String key, android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 清理内存缓存
     */
    private final void cleanupCache() {
    }
    
    /**
     * 清空所有缓存
     */
    public final void clearCache(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * 获取缓存大小信息
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCacheInfo() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lme/wcy/music/utils/CarImageLoader$CarImageSize;", "", "()V", "ALBUM_COVER_LARGE", "", "ALBUM_COVER_MEDIUM", "ALBUM_COVER_SMALL", "ALBUM_COVER_THUMB", "app_debug"})
    public static final class CarImageSize {
        public static final int ALBUM_COVER_LARGE = 512;
        public static final int ALBUM_COVER_MEDIUM = 256;
        public static final int ALBUM_COVER_SMALL = 128;
        public static final int ALBUM_COVER_THUMB = 64;
        @org.jetbrains.annotations.NotNull()
        public static final me.wcy.music.utils.CarImageLoader.CarImageSize INSTANCE = null;
        
        private CarImageSize() {
            super();
        }
    }
}