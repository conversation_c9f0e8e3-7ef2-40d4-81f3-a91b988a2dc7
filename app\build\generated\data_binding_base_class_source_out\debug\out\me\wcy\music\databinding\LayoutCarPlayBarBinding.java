// Generated by view binder compiler. Do not edit!
package me.wcy.music.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import me.wcy.music.R;

public final class LayoutCarPlayBarBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final FrameLayout flPlay;

  @NonNull
  public final ImageView ivCover;

  @NonNull
  public final ImageView ivNext;

  @NonNull
  public final ImageView ivPlay;

  @NonNull
  public final ImageView ivPlaylist;

  @NonNull
  public final ImageView ivPrev;

  @NonNull
  public final ProgressBar loadingProgress;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView tvArtist;

  @NonNull
  public final TextView tvTitle;

  private LayoutCarPlayBarBinding(@NonNull LinearLayout rootView, @NonNull FrameLayout flPlay,
      @NonNull ImageView ivCover, @NonNull ImageView ivNext, @NonNull ImageView ivPlay,
      @NonNull ImageView ivPlaylist, @NonNull ImageView ivPrev,
      @NonNull ProgressBar loadingProgress, @NonNull ProgressBar progressBar,
      @NonNull TextView tvArtist, @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.flPlay = flPlay;
    this.ivCover = ivCover;
    this.ivNext = ivNext;
    this.ivPlay = ivPlay;
    this.ivPlaylist = ivPlaylist;
    this.ivPrev = ivPrev;
    this.loadingProgress = loadingProgress;
    this.progressBar = progressBar;
    this.tvArtist = tvArtist;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutCarPlayBarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutCarPlayBarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_car_play_bar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutCarPlayBarBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.flPlay;
      FrameLayout flPlay = ViewBindings.findChildViewById(rootView, id);
      if (flPlay == null) {
        break missingId;
      }

      id = R.id.ivCover;
      ImageView ivCover = ViewBindings.findChildViewById(rootView, id);
      if (ivCover == null) {
        break missingId;
      }

      id = R.id.ivNext;
      ImageView ivNext = ViewBindings.findChildViewById(rootView, id);
      if (ivNext == null) {
        break missingId;
      }

      id = R.id.ivPlay;
      ImageView ivPlay = ViewBindings.findChildViewById(rootView, id);
      if (ivPlay == null) {
        break missingId;
      }

      id = R.id.ivPlaylist;
      ImageView ivPlaylist = ViewBindings.findChildViewById(rootView, id);
      if (ivPlaylist == null) {
        break missingId;
      }

      id = R.id.ivPrev;
      ImageView ivPrev = ViewBindings.findChildViewById(rootView, id);
      if (ivPrev == null) {
        break missingId;
      }

      id = R.id.loadingProgress;
      ProgressBar loadingProgress = ViewBindings.findChildViewById(rootView, id);
      if (loadingProgress == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tvArtist;
      TextView tvArtist = ViewBindings.findChildViewById(rootView, id);
      if (tvArtist == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new LayoutCarPlayBarBinding((LinearLayout) rootView, flPlay, ivCover, ivNext, ivPlay,
          ivPlaylist, ivPrev, loadingProgress, progressBar, tvArtist, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
