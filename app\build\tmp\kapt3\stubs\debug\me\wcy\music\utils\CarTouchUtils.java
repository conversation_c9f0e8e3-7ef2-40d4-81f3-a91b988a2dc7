package me.wcy.music.utils;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.view.HapticFeedbackConstants;
import android.view.TouchDelegate;
import android.view.View;
import android.view.ViewGroup;
import androidx.core.content.ContextCompat;
import androidx.core.view.ViewCompat;
import me.wcy.music.R;

/**
 * 车载触摸优化工具类
 * 专为Android Automotive设计，优化触摸交互体验
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u0001\u0018B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0014\u0010\u0003\u001a\u00020\u0004*\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007J\n\u0010\b\u001a\u00020\t*\u00020\u0005J\u0012\u0010\n\u001a\u00020\u0007*\u00020\u000b2\u0006\u0010\f\u001a\u00020\rJ\n\u0010\u000e\u001a\u00020\u0004*\u00020\u0005J\n\u0010\u000f\u001a\u00020\u0004*\u00020\u0005J\n\u0010\u0010\u001a\u00020\u0004*\u00020\u0005J\u0012\u0010\u0011\u001a\u00020\u0004*\u00020\u00052\u0006\u0010\u0012\u001a\u00020\u0013J\u0014\u0010\u0014\u001a\u00020\u0004*\u00020\u00052\b\b\u0002\u0010\u0015\u001a\u00020\u0007J\u0014\u0010\u0016\u001a\u00020\u0004*\u00020\u00052\b\b\u0002\u0010\u0017\u001a\u00020\u0007\u00a8\u0006\u0019"}, d2 = {"Lme/wcy/music/utils/CarTouchUtils;", "", "()V", "addCarHapticFeedback", "", "Landroid/view/View;", "feedbackType", "", "checkCarTouchCompliance", "", "getCarTouchTargetSize", "Landroid/content/Context;", "type", "Lme/wcy/music/utils/CarTouchUtils$TouchTargetType;", "optimizeCarButton", "optimizeListItemTouch", "performCarHapticFeedback", "setCarClickListener", "listener", "Landroid/view/View$OnClickListener;", "setMinTouchTarget", "minSize", "setProgressBarTouchArea", "extraPadding", "TouchTargetType", "app_debug"})
public final class CarTouchUtils {
    @org.jetbrains.annotations.NotNull()
    public static final me.wcy.music.utils.CarTouchUtils INSTANCE = null;
    
    private CarTouchUtils() {
        super();
    }
    
    /**
     * 为View设置最小触摸目标大小
     */
    public final void setMinTouchTarget(@org.jetbrains.annotations.NotNull()
    android.view.View $this$setMinTouchTarget, int minSize) {
    }
    
    /**
     * 为View添加车载优化的触觉反馈
     */
    public final void addCarHapticFeedback(@org.jetbrains.annotations.NotNull()
    android.view.View $this$addCarHapticFeedback, int feedbackType) {
    }
    
    /**
     * 设置车载优化的点击监听器（包含触觉反馈）
     */
    public final void setCarClickListener(@org.jetbrains.annotations.NotNull()
    android.view.View $this$setCarClickListener, @org.jetbrains.annotations.NotNull()
    android.view.View.OnClickListener listener) {
    }
    
    /**
     * 执行车载优化的触觉反馈
     */
    public final void performCarHapticFeedback(@org.jetbrains.annotations.NotNull()
    android.view.View $this$performCarHapticFeedback) {
    }
    
    /**
     * 为进度条设置扩展的触摸区域
     */
    public final void setProgressBarTouchArea(@org.jetbrains.annotations.NotNull()
    android.view.View $this$setProgressBarTouchArea, int extraPadding) {
    }
    
    /**
     * 优化列表项的触摸体验
     */
    public final void optimizeListItemTouch(@org.jetbrains.annotations.NotNull()
    android.view.View $this$optimizeListItemTouch) {
    }
    
    /**
     * 为按钮设置车载优化
     */
    public final void optimizeCarButton(@org.jetbrains.annotations.NotNull()
    android.view.View $this$optimizeCarButton) {
    }
    
    /**
     * 检查View是否满足车载触摸要求
     */
    public final boolean checkCarTouchCompliance(@org.jetbrains.annotations.NotNull()
    android.view.View $this$checkCarTouchCompliance) {
        return false;
    }
    
    /**
     * 获取推荐的车载触摸目标大小
     */
    public final int getCarTouchTargetSize(@org.jetbrains.annotations.NotNull()
    android.content.Context $this$getCarTouchTargetSize, @org.jetbrains.annotations.NotNull()
    me.wcy.music.utils.CarTouchUtils.TouchTargetType type) {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0005\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005\u00a8\u0006\u0006"}, d2 = {"Lme/wcy/music/utils/CarTouchUtils$TouchTargetType;", "", "(Ljava/lang/String;I)V", "SMALL", "MEDIUM", "LARGE", "app_debug"})
    public static enum TouchTargetType {
        /*public static final*/ SMALL /* = new SMALL() */,
        /*public static final*/ MEDIUM /* = new MEDIUM() */,
        /*public static final*/ LARGE /* = new LARGE() */;
        
        TouchTargetType() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<me.wcy.music.utils.CarTouchUtils.TouchTargetType> getEntries() {
            return null;
        }
    }
}