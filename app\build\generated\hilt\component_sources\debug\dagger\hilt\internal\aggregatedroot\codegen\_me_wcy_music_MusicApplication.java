package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "me.wcy.music.MusicApplication",
    rootPackage = "me.wcy.music",
    originatingRoot = "me.wcy.music.MusicApplication",
    originatingRootPackage = "me.wcy.music",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "MusicApplication",
    originatingRootSimpleNames = "MusicApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _me_wcy_music_MusicApplication {
}
