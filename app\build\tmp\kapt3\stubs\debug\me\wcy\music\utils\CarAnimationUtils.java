package me.wcy.music.utils;

import android.animation.*;
import android.content.Context;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.LinearInterpolator;
import androidx.interpolator.view.animation.FastOutSlowInInterpolator;

/**
 * 车载动画工具类
 * 专为Android Automotive设计，提供流畅的动画效果
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0002\b\n\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002JH\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\b\b\u0002\u0010\u0010\u001a\u00020\u00042\u0016\b\u0002\u0010\u0011\u001a\u0010\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u0013\u0018\u00010\u0012J\"\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\n2\b\u0010\u0017\u001a\u0004\u0018\u00010\n2\b\b\u0002\u0010\u0010\u001a\u00020\u0004J&\u0010\u0018\u001a\u00020\u0015*\u00020\n2\b\b\u0002\u0010\u0010\u001a\u00020\u00042\u0010\b\u0002\u0010\u0019\u001a\n\u0012\u0004\u0012\u00020\u0013\u0018\u00010\u001aJ\n\u0010\u001b\u001a\u00020\u0013*\u00020\nJ\n\u0010\u001c\u001a\u00020\u0013*\u00020\nJ&\u0010\u001d\u001a\u00020\u001e*\u00020\n2\b\b\u0002\u0010\u0010\u001a\u00020\u00042\u0010\b\u0002\u0010\u0019\u001a\n\u0012\u0004\u0012\u00020\u0013\u0018\u00010\u001aJ0\u0010\u001f\u001a\u00020\u001e*\u00020\n2\b\b\u0002\u0010\u0010\u001a\u00020\u00042\b\b\u0002\u0010 \u001a\u00020!2\u0010\b\u0002\u0010\u0019\u001a\n\u0012\u0004\u0012\u00020\u0013\u0018\u00010\u001aJ.\u0010\"\u001a\u00020\u0015*\u00020\n2\u0006\u0010#\u001a\u00020!2\b\b\u0002\u0010\u0010\u001a\u00020\u00042\u0010\b\u0002\u0010\u0019\u001a\n\u0012\u0004\u0012\u00020\u0013\u0018\u00010\u001aJ2\u0010$\u001a\u00020\u001e*\u00020\n2\b\b\u0002\u0010%\u001a\u00020&2\b\b\u0002\u0010\'\u001a\u00020&2\b\b\u0002\u0010\u0010\u001a\u00020\u00042\b\b\u0002\u0010(\u001a\u00020\u000eJ:\u0010)\u001a\u00020\u0015*\u00020\n2\b\b\u0002\u0010*\u001a\u00020&2\b\b\u0002\u0010+\u001a\u00020&2\b\b\u0002\u0010\u0010\u001a\u00020\u00042\u0010\b\u0002\u0010\u0019\u001a\n\u0012\u0004\u0012\u00020\u0013\u0018\u00010\u001aJ&\u0010,\u001a\u00020\u001e*\u00020\n2\b\b\u0002\u0010\u0010\u001a\u00020\u00042\u0010\b\u0002\u0010\u0019\u001a\n\u0012\u0004\u0012\u00020\u0013\u0018\u00010\u001aJ0\u0010-\u001a\u00020\u001e*\u00020\n2\b\b\u0002\u0010\u0010\u001a\u00020\u00042\b\b\u0002\u0010 \u001a\u00020!2\u0010\b\u0002\u0010\u0019\u001a\n\u0012\u0004\u0012\u00020\u0013\u0018\u00010\u001aJ\u0018\u0010.\u001a\u00020\u0013*\u00020\n2\f\u0010/\u001a\b\u0012\u0004\u0012\u00020\u00130\u001aR\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u00060"}, d2 = {"Lme/wcy/music/utils/CarAnimationUtils;", "", "()V", "DURATION_LONG", "", "DURATION_MEDIUM", "DURATION_SHORT", "animateProgress", "Landroid/animation/ValueAnimator;", "view", "Landroid/view/View;", "property", "", "fromValue", "", "toValue", "duration", "onUpdate", "Lkotlin/Function1;", "", "createPageTransitionAnimator", "Landroid/animation/AnimatorSet;", "enterView", "exitView", "bounceAnimation", "onComplete", "Lkotlin/Function0;", "disableHardwareAcceleration", "enableHardwareAcceleration", "fadeIn", "Landroid/animation/ObjectAnimator;", "fadeOut", "hideOnComplete", "", "playStateAnimation", "isPlaying", "rotateAnimation", "fromDegrees", "", "toDegrees", "repeatCount", "scaleAnimation", "fromScale", "toScale", "slideInFromRight", "slideOutToLeft", "withHardwareAcceleration", "action", "app_debug"})
public final class CarAnimationUtils {
    public static final long DURATION_SHORT = 150L;
    public static final long DURATION_MEDIUM = 300L;
    public static final long DURATION_LONG = 500L;
    @org.jetbrains.annotations.NotNull()
    public static final me.wcy.music.utils.CarAnimationUtils INSTANCE = null;
    
    private CarAnimationUtils() {
        super();
    }
    
    /**
     * 淡入动画
     */
    @org.jetbrains.annotations.NotNull()
    public final android.animation.ObjectAnimator fadeIn(@org.jetbrains.annotations.NotNull()
    android.view.View $this$fadeIn, long duration, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onComplete) {
        return null;
    }
    
    /**
     * 淡出动画
     */
    @org.jetbrains.annotations.NotNull()
    public final android.animation.ObjectAnimator fadeOut(@org.jetbrains.annotations.NotNull()
    android.view.View $this$fadeOut, long duration, boolean hideOnComplete, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onComplete) {
        return null;
    }
    
    /**
     * 滑入动画（从右侧）
     */
    @org.jetbrains.annotations.NotNull()
    public final android.animation.ObjectAnimator slideInFromRight(@org.jetbrains.annotations.NotNull()
    android.view.View $this$slideInFromRight, long duration, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onComplete) {
        return null;
    }
    
    /**
     * 滑出动画（向左侧）
     */
    @org.jetbrains.annotations.NotNull()
    public final android.animation.ObjectAnimator slideOutToLeft(@org.jetbrains.annotations.NotNull()
    android.view.View $this$slideOutToLeft, long duration, boolean hideOnComplete, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onComplete) {
        return null;
    }
    
    /**
     * 缩放动画
     */
    @org.jetbrains.annotations.NotNull()
    public final android.animation.AnimatorSet scaleAnimation(@org.jetbrains.annotations.NotNull()
    android.view.View $this$scaleAnimation, float fromScale, float toScale, long duration, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onComplete) {
        return null;
    }
    
    /**
     * 旋转动画（专辑封面）
     */
    @org.jetbrains.annotations.NotNull()
    public final android.animation.ObjectAnimator rotateAnimation(@org.jetbrains.annotations.NotNull()
    android.view.View $this$rotateAnimation, float fromDegrees, float toDegrees, long duration, int repeatCount) {
        return null;
    }
    
    /**
     * 弹跳动画
     */
    @org.jetbrains.annotations.NotNull()
    public final android.animation.AnimatorSet bounceAnimation(@org.jetbrains.annotations.NotNull()
    android.view.View $this$bounceAnimation, long duration, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onComplete) {
        return null;
    }
    
    /**
     * 进度条平滑更新动画
     */
    @org.jetbrains.annotations.NotNull()
    public final android.animation.ValueAnimator animateProgress(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.NotNull()
    java.lang.String property, int fromValue, int toValue, long duration, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onUpdate) {
        return null;
    }
    
    /**
     * 播放状态切换动画
     */
    @org.jetbrains.annotations.NotNull()
    public final android.animation.AnimatorSet playStateAnimation(@org.jetbrains.annotations.NotNull()
    android.view.View $this$playStateAnimation, boolean isPlaying, long duration, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onComplete) {
        return null;
    }
    
    /**
     * 启用硬件加速
     */
    public final void enableHardwareAcceleration(@org.jetbrains.annotations.NotNull()
    android.view.View $this$enableHardwareAcceleration) {
    }
    
    /**
     * 禁用硬件加速
     */
    public final void disableHardwareAcceleration(@org.jetbrains.annotations.NotNull()
    android.view.View $this$disableHardwareAcceleration) {
    }
    
    /**
     * 为动画启用硬件加速
     */
    public final void withHardwareAcceleration(@org.jetbrains.annotations.NotNull()
    android.view.View $this$withHardwareAcceleration, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> action) {
    }
    
    /**
     * 创建页面切换动画
     */
    @org.jetbrains.annotations.NotNull()
    public final android.animation.AnimatorSet createPageTransitionAnimator(@org.jetbrains.annotations.NotNull()
    android.view.View enterView, @org.jetbrains.annotations.Nullable()
    android.view.View exitView, long duration) {
        return null;
    }
}