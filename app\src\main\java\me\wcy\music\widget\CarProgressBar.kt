package me.wcy.music.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.core.content.ContextCompat
import me.wcy.music.R
import kotlin.math.max
import kotlin.math.min

/**
 * 车载优化的进度条
 * 支持显示缓存进度，类似YouTube的灰色缓存条
 */
class CarProgressBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    companion object {
        private const val MIN_TOUCH_TARGET_SIZE = 48f // dp
    }

    // 进度相关
    var max: Int = 100
        set(value) {
            field = max(1, value)
            invalidate()
        }

    var progress: Int = 0
        set(value) {
            field = value.coerceIn(0, max)
            invalidate()
        }

    var secondaryProgress: Int = 0
        set(value) {
            field = value.coerceIn(0, max)
            invalidate()
        }

    // 缓存进度（0-100）
    var cacheProgress: Float = 0f
        set(value) {
            field = value.coerceIn(0f, 100f)
            invalidate()
        }

    // 画笔
    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(context, R.color.car_bg_tertiary)
        style = Paint.Style.FILL
    }

    private val cachePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(context, R.color.car_text_tertiary)
        style = Paint.Style.FILL
    }

    private val secondaryPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(context, R.color.car_text_secondary)
        style = Paint.Style.FILL
    }

    private val progressPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(context, R.color.car_theme_primary)
        style = Paint.Style.FILL
    }

    private val thumbPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = ContextCompat.getColor(context, R.color.car_theme_primary)
        style = Paint.Style.FILL
    }

    // 尺寸
    private val progressHeight = context.resources.getDimension(R.dimen.car_progress_height)
    private val thumbRadius = context.resources.getDimension(R.dimen.car_progress_thumb_radius)
    private val minTouchTargetSize = MIN_TOUCH_TARGET_SIZE * context.resources.displayMetrics.density

    // 拖拽状态
    private var isDragging = false
    private var dragProgress = 0

    // 回调
    var onProgressChangeListener: OnProgressChangeListener? = null

    interface OnProgressChangeListener {
        fun onProgressChanged(progress: Int, fromUser: Boolean)
        fun onStartTrackingTouch()
        fun onStopTrackingTouch()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val width = MeasureSpec.getSize(widthMeasureSpec)
        val height = max(progressHeight.toInt(), minTouchTargetSize.toInt())
        setMeasuredDimension(width, height)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val width = width.toFloat()
        val height = height.toFloat()
        val centerY = height / 2
        val progressTop = centerY - progressHeight / 2
        val progressBottom = centerY + progressHeight / 2

        val progressRect = RectF(0f, progressTop, width, progressBottom)
        val cornerRadius = progressHeight / 2

        // 绘制背景
        canvas.drawRoundRect(progressRect, cornerRadius, cornerRadius, backgroundPaint)

        // 绘制缓存进度
        if (cacheProgress > 0) {
            val cacheWidth = width * (cacheProgress / 100f)
            val cacheRect = RectF(0f, progressTop, cacheWidth, progressBottom)
            canvas.drawRoundRect(cacheRect, cornerRadius, cornerRadius, cachePaint)
        }

        // 绘制次要进度（缓冲进度）
        if (secondaryProgress > 0) {
            val secondaryWidth = width * (secondaryProgress.toFloat() / max)
            val secondaryRect = RectF(0f, progressTop, secondaryWidth, progressBottom)
            canvas.drawRoundRect(secondaryRect, cornerRadius, cornerRadius, secondaryPaint)
        }

        // 绘制主进度
        val currentProgress = if (isDragging) dragProgress else progress
        if (currentProgress > 0) {
            val progressWidth = width * (currentProgress.toFloat() / max)
            val mainProgressRect = RectF(0f, progressTop, progressWidth, progressBottom)
            canvas.drawRoundRect(mainProgressRect, cornerRadius, cornerRadius, progressPaint)

            // 绘制拖拽圆点
            if (isDragging || currentProgress > 0) {
                val thumbX = progressWidth
                canvas.drawCircle(thumbX, centerY, thumbRadius, thumbPaint)
            }
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (!isEnabled) {
            return false
        }

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                isDragging = true
                updateDragProgress(event.x)
                onProgressChangeListener?.onStartTrackingTouch()
                parent?.requestDisallowInterceptTouchEvent(true)
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDragging) {
                    updateDragProgress(event.x)
                }
                return true
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (isDragging) {
                    isDragging = false
                    progress = dragProgress
                    onProgressChangeListener?.onProgressChanged(progress, true)
                    onProgressChangeListener?.onStopTrackingTouch()
                    parent?.requestDisallowInterceptTouchEvent(false)
                }
                return true
            }
        }

        return super.onTouchEvent(event)
    }

    private fun updateDragProgress(x: Float) {
        val width = width.toFloat()
        val percentage = (x / width).coerceIn(0f, 1f)
        dragProgress = (max * percentage).toInt()
        onProgressChangeListener?.onProgressChanged(dragProgress, true)
        invalidate()
    }

    /**
     * 设置进度（带动画）
     */
    fun setProgressSmooth(targetProgress: Int, duration: Long = 300) {
        // TODO: 可以添加动画效果
        progress = targetProgress
    }

    /**
     * 更新缓存状态
     */
    fun updateCacheState(cachePercentage: Float, bufferPercentage: Int) {
        cacheProgress = cachePercentage
        secondaryProgress = bufferPercentage
        invalidate()
    }
}
