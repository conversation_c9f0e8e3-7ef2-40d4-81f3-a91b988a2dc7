package me.wcy.music.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.widget.ImageView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestBuilder;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.Target;
import kotlinx.coroutines.Dispatchers;
import me.wcy.music.R;
import java.lang.ref.WeakReference;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000&\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u001e\u0010\u0000\u001a\u00020\u0001*\u00020\u00022\b\u0010\u0003\u001a\u0004\u0018\u00010\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u001a8\u0010\u0007\u001a\u00020\u0001*\u00020\u00022\b\u0010\u0003\u001a\u0004\u0018\u00010\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u00062\u0018\b\u0002\u0010\b\u001a\u0012\u0012\u0006\u0012\u0004\u0018\u00010\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\t\u001a\u001e\u0010\u000b\u001a\u00020\u0001*\u00020\u00022\b\u0010\u0003\u001a\u0004\u0018\u00010\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\f"}, d2 = {"loadCarAlbumCover", "", "Landroid/widget/ImageView;", "url", "", "corners", "", "loadCarAlbumCoverLarge", "onLoadComplete", "Lkotlin/Function1;", "Landroid/graphics/Bitmap;", "loadCarAlbumCoverSmall", "app_debug"})
public final class CarImageLoaderKt {
    
    /**
     * 扩展函数 - 简化调用
     */
    public static final void loadCarAlbumCover(@org.jetbrains.annotations.NotNull()
    android.widget.ImageView $this$loadCarAlbumCover, @org.jetbrains.annotations.Nullable()
    java.lang.Object url, int corners) {
    }
    
    public static final void loadCarAlbumCoverLarge(@org.jetbrains.annotations.NotNull()
    android.widget.ImageView $this$loadCarAlbumCoverLarge, @org.jetbrains.annotations.Nullable()
    java.lang.Object url, int corners, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super android.graphics.Bitmap, kotlin.Unit> onLoadComplete) {
    }
    
    public static final void loadCarAlbumCoverSmall(@org.jetbrains.annotations.NotNull()
    android.widget.ImageView $this$loadCarAlbumCoverSmall, @org.jetbrains.annotations.Nullable()
    java.lang.Object url, int corners) {
    }
}