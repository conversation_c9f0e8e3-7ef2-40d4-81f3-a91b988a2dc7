package me.wcy.music.service.cache

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 缓存相关的依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object CacheModule {

    @Provides
    @Singleton
    fun provideAudioCacheManager(
        @ApplicationContext context: Context
    ): AudioCacheManager {
        return AudioCacheManager(context)
    }
}
