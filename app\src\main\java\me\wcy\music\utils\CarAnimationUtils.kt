package me.wcy.music.utils

import android.animation.*
import android.content.Context
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.DecelerateInterpolator
import android.view.animation.LinearInterpolator
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.interpolator.view.animation.FastOutSlowInInterpolator

/**
 * 车载动画工具类
 * 专为Android Automotive设计，提供流畅的动画效果
 */
object CarAnimationUtils {

    // 动画时长常量
    const val DURATION_SHORT = 150L
    const val DURATION_MEDIUM = 300L
    const val DURATION_LONG = 500L

    /**
     * 淡入动画
     */
    fun View.fadeIn(
        duration: Long = DURATION_MEDIUM,
        onComplete: (() -> Unit)? = null
    ): ObjectAnimator {
        alpha = 0f
        visibility = View.VISIBLE

        return ObjectAnimator.ofFloat(this, "alpha", 0f, 1f).apply {
            this.duration = duration
            interpolator = DecelerateInterpolator()
            doOnEnd { onComplete?.invoke() }
            start()
        }
    }

    /**
     * 淡出动画
     */
    fun View.fadeOut(
        duration: Long = DURATION_MEDIUM,
        hideOnComplete: Boolean = true,
        onComplete: (() -> Unit)? = null
    ): ObjectAnimator {
        return ObjectAnimator.ofFloat(this, "alpha", alpha, 0f).apply {
            this.duration = duration
            interpolator = AccelerateDecelerateInterpolator()
            doOnEnd {
                if (hideOnComplete) {
                    visibility = View.GONE
                }
                onComplete?.invoke()
            }
            start()
        }
    }

    /**
     * 滑入动画（从右侧）
     */
    fun View.slideInFromRight(
        duration: Long = DURATION_MEDIUM,
        onComplete: (() -> Unit)? = null
    ): ObjectAnimator {
        val startX = context.resources.displayMetrics.widthPixels.toFloat()
        translationX = startX
        visibility = View.VISIBLE

        return ObjectAnimator.ofFloat(this, "translationX", startX, 0f).apply {
            this.duration = duration
            interpolator = FastOutSlowInInterpolator()
            doOnEnd { onComplete?.invoke() }
            start()
        }
    }

    /**
     * 滑出动画（向左侧）
     */
    fun View.slideOutToLeft(
        duration: Long = DURATION_MEDIUM,
        hideOnComplete: Boolean = true,
        onComplete: (() -> Unit)? = null
    ): ObjectAnimator {
        val endX = -width.toFloat()

        return ObjectAnimator.ofFloat(this, "translationX", translationX, endX).apply {
            this.duration = duration
            interpolator = FastOutSlowInInterpolator()
            doOnEnd {
                if (hideOnComplete) {
                    visibility = View.GONE
                    translationX = 0f
                }
                onComplete?.invoke()
            }
            start()
        }
    }

    /**
     * 缩放动画
     */
    fun View.scaleAnimation(
        fromScale: Float = 0.8f,
        toScale: Float = 1.0f,
        duration: Long = DURATION_SHORT,
        onComplete: (() -> Unit)? = null
    ): AnimatorSet {
        val scaleX = ObjectAnimator.ofFloat(this, "scaleX", fromScale, toScale)
        val scaleY = ObjectAnimator.ofFloat(this, "scaleY", fromScale, toScale)

        return AnimatorSet().apply {
            playTogether(scaleX, scaleY)
            this.duration = duration
            interpolator = FastOutSlowInInterpolator()
            doOnEnd { onComplete?.invoke() }
            start()
        }
    }

    /**
     * 旋转动画（专辑封面）
     */
    fun View.rotateAnimation(
        fromDegrees: Float = 0f,
        toDegrees: Float = 360f,
        duration: Long = 20000L,
        repeatCount: Int = ValueAnimator.INFINITE
    ): ObjectAnimator {
        return ObjectAnimator.ofFloat(this, "rotation", fromDegrees, toDegrees).apply {
            this.duration = duration
            this.repeatCount = repeatCount
            this.repeatMode = ObjectAnimator.RESTART
            interpolator = LinearInterpolator()
            start()
        }
    }

    /**
     * 弹跳动画
     */
    fun View.bounceAnimation(
        duration: Long = DURATION_MEDIUM,
        onComplete: (() -> Unit)? = null
    ): AnimatorSet {
        val scaleXDown = ObjectAnimator.ofFloat(this, "scaleX", 1f, 0.9f)
        val scaleYDown = ObjectAnimator.ofFloat(this, "scaleY", 1f, 0.9f)
        val scaleXUp = ObjectAnimator.ofFloat(this, "scaleX", 0.9f, 1f)
        val scaleYUp = ObjectAnimator.ofFloat(this, "scaleY", 0.9f, 1f)

        val downSet = AnimatorSet().apply {
            playTogether(scaleXDown, scaleYDown)
            this.duration = duration / 2
        }

        val upSet = AnimatorSet().apply {
            playTogether(scaleXUp, scaleYUp)
            this.duration = duration / 2
        }

        return AnimatorSet().apply {
            playSequentially(downSet, upSet)
            interpolator = FastOutSlowInInterpolator()
            doOnEnd { onComplete?.invoke() }
            start()
        }
    }

    /**
     * 进度条平滑更新动画
     */
    fun animateProgress(
        fromValue: Int,
        toValue: Int,
        duration: Long = DURATION_SHORT,
        onUpdate: ((Int) -> Unit)? = null
    ): ValueAnimator {
        return ValueAnimator.ofInt(fromValue, toValue).apply {
            this.duration = duration
            interpolator = DecelerateInterpolator()
            addUpdateListener { animator ->
                val value = animator.animatedValue as Int
                onUpdate?.invoke(value)
            }
            start()
        }
    }

    /**
     * 播放状态切换动画
     */
    fun View.playStateAnimation(
        isPlaying: Boolean,
        duration: Long = DURATION_SHORT,
        onComplete: (() -> Unit)? = null
    ): AnimatorSet {
        val targetScale = if (isPlaying) 1.1f else 1.0f
        val targetAlpha = if (isPlaying) 1.0f else 0.8f

        val scaleX = ObjectAnimator.ofFloat(this, "scaleX", scaleX, targetScale)
        val scaleY = ObjectAnimator.ofFloat(this, "scaleY", scaleY, targetScale)
        val alpha = ObjectAnimator.ofFloat(this, "alpha", alpha, targetAlpha)

        return AnimatorSet().apply {
            playTogether(scaleX, scaleY, alpha)
            this.duration = duration
            interpolator = FastOutSlowInInterpolator()
            doOnEnd { onComplete?.invoke() }
            start()
        }
    }

    /**
     * 启用硬件加速
     */
    fun View.enableHardwareAcceleration() {
        setLayerType(View.LAYER_TYPE_HARDWARE, null)
    }

    /**
     * 禁用硬件加速
     */
    fun View.disableHardwareAcceleration() {
        setLayerType(View.LAYER_TYPE_NONE, null)
    }

    /**
     * 为动画启用硬件加速
     */
    fun View.withHardwareAcceleration(action: () -> Unit) {
        enableHardwareAcceleration()
        action()
        post { disableHardwareAcceleration() }
    }

    /**
     * 创建页面切换动画
     */
    fun createPageTransitionAnimator(
        enterView: View,
        exitView: View?,
        duration: Long = DURATION_MEDIUM
    ): AnimatorSet {
        val animators = mutableListOf<Animator>()

        // 进入动画
        enterView.alpha = 0f
        enterView.translationX = 100f
        animators.add(ObjectAnimator.ofFloat(enterView, "alpha", 0f, 1f))
        animators.add(ObjectAnimator.ofFloat(enterView, "translationX", 100f, 0f))

        // 退出动画
        exitView?.let { view ->
            animators.add(ObjectAnimator.ofFloat(view, "alpha", 1f, 0f))
            animators.add(ObjectAnimator.ofFloat(view, "translationX", 0f, -100f))
        }

        return AnimatorSet().apply {
            playTogether(animators)
            this.duration = duration
            interpolator = FastOutSlowInInterpolator()
        }
    }
}
