package me.wcy.music.utils;

import android.app.Activity;
import android.os.Build;
import android.view.View;
import android.view.Window;
import android.view.WindowInsets;
import android.view.WindowInsetsController;
import android.view.WindowManager;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;

/**
 * 车载窗口工具类
 * 提供现代化的全屏模式和窗口管理功能，兼容不同Android版本
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0002\b\u0007\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\n\u0010\u0003\u001a\u00020\u0004*\u00020\u0005J\n\u0010\u0006\u001a\u00020\u0004*\u00020\u0005J\f\u0010\u0007\u001a\u0004\u0018\u00010\b*\u00020\u0005J\u0012\u0010\t\u001a\u00020\u0004*\u00020\u00052\u0006\u0010\n\u001a\u00020\u000bJ\n\u0010\f\u001a\u00020\u0004*\u00020\u0005J\n\u0010\r\u001a\u00020\u000b*\u00020\u0005J\u0012\u0010\u000e\u001a\u00020\u0004*\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011J\u0012\u0010\u0012\u001a\u00020\u0004*\u00020\u00052\u0006\u0010\u0013\u001a\u00020\u000bJ\u0012\u0010\u0014\u001a\u00020\u0004*\u00020\u00052\u0006\u0010\u0015\u001a\u00020\u0016J\u0012\u0010\u0017\u001a\u00020\u0004*\u00020\u00052\u0006\u0010\u0013\u001a\u00020\u000bJ\n\u0010\u0018\u001a\u00020\u0004*\u00020\u0005J\n\u0010\u0019\u001a\u00020\u0004*\u00020\u0005J\f\u0010\u001a\u001a\u00020\u0004*\u00020\u0005H\u0002J\f\u0010\u001b\u001a\u00020\u0004*\u00020\u0005H\u0002J\n\u0010\u001c\u001a\u00020\u0004*\u00020\u0005\u00a8\u0006\u001d"}, d2 = {"Lme/wcy/music/utils/CarWindowUtils;", "", "()V", "applyCarThemeWindow", "", "Landroid/app/Activity;", "enableHardwareAcceleration", "getSafeAreaInsets", "Landroidx/core/view/WindowInsetsCompat;", "handleCarWindowFocusChanged", "hasFocus", "", "hideSystemBars", "isFullscreen", "setCarBackground", "Landroid/view/Window;", "colorRes", "", "setNavigationBarButtonColor", "isDark", "setScreenBrightness", "brightness", "", "setStatusBarTextColor", "setupCarFullscreenMode", "setupCarWindowAttributes", "setupLegacyFullscreen", "setupModernFullscreen", "showSystemBars", "app_debug"})
public final class CarWindowUtils {
    @org.jetbrains.annotations.NotNull()
    public static final me.wcy.music.utils.CarWindowUtils INSTANCE = null;
    
    private CarWindowUtils() {
        super();
    }
    
    /**
     * 设置车载全屏沉浸式模式
     * 使用现代API替代弃用的systemUiVisibility
     */
    public final void setupCarFullscreenMode(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$setupCarFullscreenMode) {
    }
    
    /**
     * Android 11+ 现代全屏模式
     */
    private final void setupModernFullscreen(android.app.Activity $this$setupModernFullscreen) {
    }
    
    /**
     * Android 10及以下传统全屏模式
     */
    @kotlin.Suppress(names = {"DEPRECATION"})
    private final void setupLegacyFullscreen(android.app.Activity $this$setupLegacyFullscreen) {
    }
    
    /**
     * 显示系统栏
     */
    public final void showSystemBars(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$showSystemBars) {
    }
    
    /**
     * 隐藏系统栏
     */
    public final void hideSystemBars(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$hideSystemBars) {
    }
    
    /**
     * 设置状态栏文字颜色
     */
    public final void setStatusBarTextColor(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$setStatusBarTextColor, boolean isDark) {
    }
    
    /**
     * 设置导航栏按钮颜色
     */
    public final void setNavigationBarButtonColor(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$setNavigationBarButtonColor, boolean isDark) {
    }
    
    /**
     * 设置窗口背景（使用现代API）
     */
    public final void setCarBackground(@org.jetbrains.annotations.NotNull()
    android.view.Window $this$setCarBackground, int colorRes) {
    }
    
    /**
     * 启用硬件加速
     */
    public final void enableHardwareAcceleration(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$enableHardwareAcceleration) {
    }
    
    /**
     * 设置屏幕亮度
     */
    public final void setScreenBrightness(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$setScreenBrightness, float brightness) {
    }
    
    /**
     * 检查是否为全屏模式
     */
    public final boolean isFullscreen(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$isFullscreen) {
        return false;
    }
    
    /**
     * 设置车载优化的窗口属性
     */
    public final void setupCarWindowAttributes(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$setupCarWindowAttributes) {
    }
    
    /**
     * 处理窗口焦点变化（车载环境优化）
     */
    public final void handleCarWindowFocusChanged(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$handleCarWindowFocusChanged, boolean hasFocus) {
    }
    
    /**
     * 获取安全区域内边距
     */
    @org.jetbrains.annotations.Nullable()
    public final androidx.core.view.WindowInsetsCompat getSafeAreaInsets(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$getSafeAreaInsets) {
        return null;
    }
    
    /**
     * 应用车载主题窗口样式
     */
    public final void applyCarThemeWindow(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$applyCarThemeWindow) {
    }
}