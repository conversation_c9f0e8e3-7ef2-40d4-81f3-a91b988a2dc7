<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_playing_control" modulePackage="me.wcy.music" filePath="app\src\main\res\layout\activity_playing_control.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_playing_control_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="168" endOffset="14"/></Target><Target id="@+id/llActions" view="LinearLayout"><Expressions/><location startLine="8" startOffset="4" endLine="48" endOffset="18"/></Target><Target id="@+id/ivLike" view="ImageView"><Expressions/><location startLine="20" startOffset="8" endLine="27" endOffset="58"/></Target><Target id="@+id/ivDownload" view="ImageView"><Expressions/><location startLine="34" startOffset="8" endLine="42" endOffset="53"/></Target><Target id="@+id/llProgress" view="LinearLayout"><Expressions/><location startLine="50" startOffset="4" endLine="83" endOffset="18"/></Target><Target id="@+id/tvCurrentTime" view="TextView"><Expressions/><location startLine="59" startOffset="8" endLine="66" endOffset="37"/></Target><Target id="@+id/sbProgress" view="me.wcy.music.widget.CarProgressBar"><Expressions/><location startLine="68" startOffset="8" endLine="73" endOffset="52"/></Target><Target id="@+id/tvTotalTime" view="TextView"><Expressions/><location startLine="75" startOffset="8" endLine="82" endOffset="37"/></Target><Target id="@+id/ivMode" view="ImageView"><Expressions/><location startLine="93" startOffset="8" endLine="103" endOffset="53"/></Target><Target id="@+id/ivPrev" view="ImageView"><Expressions/><location startLine="105" startOffset="8" endLine="115" endOffset="53"/></Target><Target id="@+id/flPlay" view="FrameLayout"><Expressions/><location startLine="117" startOffset="8" endLine="142" endOffset="21"/></Target><Target id="@+id/ivPlay" view="ImageView"><Expressions/><location startLine="125" startOffset="12" endLine="132" endOffset="57"/></Target><Target id="@+id/loadingProgress" view="ProgressBar"><Expressions/><location startLine="134" startOffset="12" endLine="141" endOffset="71"/></Target><Target id="@+id/ivNext" view="ImageView"><Expressions/><location startLine="144" startOffset="8" endLine="154" endOffset="53"/></Target><Target id="@+id/ivPlaylist" view="ImageView"><Expressions/><location startLine="156" startOffset="8" endLine="166" endOffset="53"/></Target></Targets></Layout>