<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_playing_control" modulePackage="me.wcy.music" filePath="app\src\main\res\layout\activity_playing_control.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_playing_control_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="150" endOffset="14"/></Target><Target id="@+id/llActions" view="LinearLayout"><Expressions/><location startLine="8" startOffset="4" endLine="48" endOffset="18"/></Target><Target id="@+id/ivLike" view="ImageView"><Expressions/><location startLine="20" startOffset="8" endLine="27" endOffset="58"/></Target><Target id="@+id/ivDownload" view="ImageView"><Expressions/><location startLine="34" startOffset="8" endLine="42" endOffset="53"/></Target><Target id="@+id/llProgress" view="LinearLayout"><Expressions/><location startLine="50" startOffset="4" endLine="81" endOffset="18"/></Target><Target id="@+id/tvCurrentTime" view="TextView"><Expressions/><location startLine="59" startOffset="8" endLine="65" endOffset="37"/></Target><Target id="@+id/sbProgress" view="me.wcy.music.widget.CarProgressBar"><Expressions/><location startLine="67" startOffset="8" endLine="72" endOffset="51"/></Target><Target id="@+id/tvTotalTime" view="TextView"><Expressions/><location startLine="74" startOffset="8" endLine="80" endOffset="37"/></Target><Target id="@+id/ivMode" view="ImageView"><Expressions/><location startLine="90" startOffset="8" endLine="97" endOffset="53"/></Target><Target id="@+id/ivPrev" view="ImageView"><Expressions/><location startLine="99" startOffset="8" endLine="106" endOffset="53"/></Target><Target id="@+id/flPlay" view="FrameLayout"><Expressions/><location startLine="108" startOffset="8" endLine="130" endOffset="21"/></Target><Target id="@+id/ivPlay" view="ImageView"><Expressions/><location startLine="114" startOffset="12" endLine="119" endOffset="57"/></Target><Target id="@+id/loadingProgress" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="121" startOffset="12" endLine="129" endOffset="42"/></Target><Target id="@+id/ivNext" view="ImageView"><Expressions/><location startLine="132" startOffset="8" endLine="139" endOffset="53"/></Target><Target id="@+id/ivPlaylist" view="ImageView"><Expressions/><location startLine="141" startOffset="8" endLine="148" endOffset="53"/></Target></Targets></Layout>