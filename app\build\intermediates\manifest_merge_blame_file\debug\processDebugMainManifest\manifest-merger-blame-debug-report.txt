1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="me.wcy.music"
4    android:versionCode="2030001"
5    android:versionName="2.3.0-beta01" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="36" />
10
11    <!-- 基础权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:9:5-81
15-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:9:22-78
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
16-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:10:5-77
16-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:10:22-74
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
17-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:11:5-92
17-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:11:22-89
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:12:5-80
18-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:12:22-77
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:13:5-68
19-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:13:22-65
20    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
20-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:14:5-75
20-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:14:22-72
21    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
21-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:15:5-77
21-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:15:22-74
22
23    <!-- Android Automotive 特性声明 -->
24    <uses-feature
24-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:18:5-20:36
25        android:name="android.hardware.type.automotive"
25-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:19:9-56
26        android:required="false" />
26-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:20:9-33
27
28    <!-- 车载音频应用特性 -->
29    <uses-feature
29-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:23:5-25:36
30        android:name="android.software.leanback"
30-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:24:9-49
31        android:required="false" />
31-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:25:9-33
32
33    <uses-permission android:name="android.permission.VIBRATE" />
33-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:8:5-66
33-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:8:22-63
34    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
34-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:11:5-76
34-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:11:22-73
35    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
35-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:13:5-75
35-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:13:22-72
36    <uses-permission android:name="android.permission.CAMERA" />
36-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:14:5-65
36-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:14:22-62
37
38    <queries>
38-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:16:5-20:15
39        <intent>
39-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:17:9-19:18
40            <action android:name="android.intent.action.SEND" />
40-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:18:13-65
40-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:18:21-62
41        </intent>
42        <intent>
42-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:10:9-12:18
43            <action android:name="android.intent.action.MAIN" />
43-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:61:17-69
43-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:61:25-66
44        </intent>
45        <intent>
45-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:13:9-15:18
46            <action android:name="android.intent.action.VIEW" />
46-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:70:17-69
46-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:70:25-66
47        </intent>
48    </queries>
49
50    <uses-feature android:name="android.hardware.camera" />
50-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:13:5-60
50-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:13:19-57
51    <uses-feature android:name="android.hardware.camera.autofocus" />
51-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:14:5-70
51-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:14:19-67
52
53    <uses-permission android:name="android.permission.FLASHLIGHT" />
53-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:16:5-69
53-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:16:22-66
54    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
54-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:20:5-84
54-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:20:22-81
55
56    <permission
56-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
57        android:name="me.wcy.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
57-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
58        android:protectionLevel="signature" />
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
59
60    <uses-permission android:name="me.wcy.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
60-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
60-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
61
62    <application
62-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:27:5-95:19
63        android:name="me.wcy.music.MusicApplication"
63-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:28:9-41
64        android:allowBackup="true"
64-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:29:9-35
65        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
65-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
66        android:debuggable="true"
67        android:extractNativeLibs="true"
68        android:icon="@drawable/ic_launcher"
68-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:30:9-45
69        android:label="@string/app_name"
69-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:31:9-41
70        android:networkSecurityConfig="@xml/network_security_config"
70-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:32:9-69
71        android:roundIcon="@drawable/ic_launcher_round"
71-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:33:9-56
72        android:supportsRtl="true"
72-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:26:9-35
73        android:testOnly="true"
74        android:theme="@style/AppTheme" >
74-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:34:9-40
75        <service
75-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:36:9-43:19
76            android:name="me.wcy.music.service.MusicService"
76-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:37:13-49
77            android:exported="true"
77-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:38:13-36
78            android:foregroundServiceType="mediaPlayback" >
78-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:39:13-58
79            <intent-filter>
79-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:40:13-42:29
80                <action android:name="androidx.media3.session.MediaSessionService" />
80-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:41:17-86
80-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:41:25-83
81            </intent-filter>
82        </service>
83
84        <receiver
84-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:45:9-51:20
85            android:name="me.wcy.music.download.DownloadReceiver"
85-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:46:13-54
86            android:exported="true" >
86-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:47:13-36
87            <intent-filter>
87-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:48:13-50:29
88                <action android:name="android.intent.action.DOWNLOAD_COMPLETE" />
88-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:49:17-82
88-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:49:25-79
89            </intent-filter>
90        </receiver>
91
92        <activity
92-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:53:9-74:20
93            android:name="me.wcy.music.main.MainActivity"
93-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:54:13-46
94            android:configChanges="orientation|screenSize|keyboardHidden"
94-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:59:13-74
95            android:exported="true"
95-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:55:13-36
96            android:label="@string/app_name"
96-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:56:13-45
97            android:launchMode="singleTop"
97-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:57:13-43
98            android:screenOrientation="landscape" >
98-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:58:13-50
99            <intent-filter>
99-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:60:13-66:29
100                <action android:name="android.intent.action.MAIN" />
100-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:61:17-69
100-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:61:25-66
101
102                <category android:name="android.intent.category.LAUNCHER" />
102-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:63:17-77
102-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:63:27-74
103                <!-- Android Automotive 车载应用分类 -->
104                <category android:name="android.intent.category.CAR_LAUNCHER" />
104-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:65:17-81
104-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:65:27-78
105            </intent-filter>
106
107            <!-- 媒体播放器意图过滤器 -->
108            <intent-filter>
108-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:69:13-73:29
109                <action android:name="android.intent.action.VIEW" />
109-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:70:17-69
109-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:70:25-66
110
111                <category android:name="android.intent.category.DEFAULT" />
111-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:71:17-76
111-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:71:27-73
112
113                <data android:mimeType="audio/*" />
113-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:72:17-52
113-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:72:23-49
114            </intent-filter>
115        </activity>
116        <activity
116-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:75:9-78:77
117            android:name="me.wcy.music.common.MusicFragmentContainerActivity"
117-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:76:13-66
118            android:configChanges="orientation|screenSize|keyboardHidden"
118-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:78:13-74
119            android:screenOrientation="landscape" />
119-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:77:13-50
120        <activity
120-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:79:9-83:77
121            android:name="me.wcy.music.main.SettingsActivity"
121-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:80:13-50
122            android:configChanges="orientation|screenSize|keyboardHidden"
122-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:83:13-74
123            android:label="@string/menu_setting"
123-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:81:13-49
124            android:screenOrientation="landscape" />
124-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:82:13-50
125        <activity
125-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:84:9-88:77
126            android:name="me.wcy.music.main.AboutActivity"
126-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:85:13-47
127            android:configChanges="orientation|screenSize|keyboardHidden"
127-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:88:13-74
128            android:label="@string/menu_about"
128-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:86:13-47
129            android:screenOrientation="landscape" />
129-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:87:13-50
130        <activity
130-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:89:9-94:53
131            android:name="me.wcy.music.main.playing.PlayingActivity"
131-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:90:13-69
132            android:configChanges="orientation|screenSize|keyboardHidden"
132-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:93:13-74
133            android:launchMode="singleTop"
133-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:91:13-43
134            android:screenOrientation="landscape"
134-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:92:13-50
135            android:theme="@style/AppTheme.Popup" />
135-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:94:13-50
136        <activity
136-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:23:9-25:50
137            android:name="top.wangchenyan.common.ui.activity.FragmentContainerActivity"
137-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:24:13-88
138            android:screenOrientation="behind" />
138-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:25:13-47
139
140        <meta-data
140-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:11:9-13:36
141            android:name="android.notch_support"
141-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:12:13-49
142            android:value="true" /> <!-- PermissonActivity -->
142-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:13:13-33
143        <activity
143-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:15:9-17:75
144            android:name="com.lxj.xpopup.util.XPermission$PermissionActivity"
144-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:16:13-78
145            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
145-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:17:13-72
146
147        <provider
147-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:27:9-35:20
148            android:name="cn.bertsir.zbar.utils.QrFileProvider"
148-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:28:13-64
149            android:authorities="me.wcy.music.zbar.FileProvider"
149-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:29:13-69
150            android:exported="false"
150-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:30:13-37
151            android:grantUriPermissions="true" >
151-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:31:13-47
152            <meta-data
152-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:32:13-34:57
153                android:name="android.support.FILE_PROVIDER_PATHS"
153-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:33:17-67
154                android:resource="@xml/qr_file_paths" />
154-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:34:17-54
155        </provider>
156
157        <activity
157-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:37:9-39:77
158            android:name="cn.bertsir.zbar.QRActivity"
158-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:38:13-54
159            android:configChanges="keyboardHidden|orientation|screenSize" />
159-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:39:13-74
160        <activity
160-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:40:9-42:58
161            android:name="cn.bertsir.zbar.utils.PermissionUtils$PermissionActivity"
161-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:41:13-84
162            android:theme="@style/ActivityTranslucent" />
162-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:42:13-55
163        <activity android:name="com.soundcloud.android.crop.CropImageActivity" />
163-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:43:9-82
163-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:43:19-79
164        <activity
164-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:19:9-24:75
165            android:name="com.blankj.utilcode.util.UtilsTransActivity4MainProcess"
165-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:20:13-83
166            android:configChanges="orientation|keyboardHidden|screenSize"
166-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:21:13-74
167            android:exported="false"
167-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:22:13-37
168            android:theme="@style/ActivityTranslucent"
168-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:23:13-55
169            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
169-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:24:13-72
170        <activity
170-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:25:9-31:75
171            android:name="com.blankj.utilcode.util.UtilsTransActivity"
171-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:26:13-71
172            android:configChanges="orientation|keyboardHidden|screenSize"
172-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:27:13-74
173            android:exported="false"
173-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:28:13-37
174            android:multiprocess="true"
174-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:29:13-40
175            android:theme="@style/ActivityTranslucent"
175-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:30:13-55
176            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
176-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:31:13-72
177
178        <provider
178-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:33:9-41:20
179            android:name="com.blankj.utilcode.util.UtilsFileProvider"
179-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:34:13-70
180            android:authorities="me.wcy.music.utilcode.fileprovider"
180-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:35:13-73
181            android:exported="false"
181-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:36:13-37
182            android:grantUriPermissions="true" >
182-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:37:13-47
183            <meta-data
183-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:32:13-34:57
184                android:name="android.support.FILE_PROVIDER_PATHS"
184-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:33:17-67
185                android:resource="@xml/util_code_provider_paths" />
185-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:34:17-54
186        </provider>
187
188        <service
188-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:43:9-49:19
189            android:name="com.blankj.utilcode.util.MessengerUtils$ServerService"
189-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:44:13-81
190            android:exported="false" >
190-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:45:13-37
191            <intent-filter>
191-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:46:13-48:29
192                <action android:name="me.wcy.music.messenger" />
192-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:17-69
192-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:25-66
193            </intent-filter>
194        </service>
195        <service
195-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
196            android:name="androidx.room.MultiInstanceInvalidationService"
196-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
197            android:directBootAware="true"
197-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
198            android:exported="false" />
198-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
199
200        <provider
200-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
201            android:name="androidx.startup.InitializationProvider"
201-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
202            android:authorities="me.wcy.music.androidx-startup"
202-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
203            android:exported="false" >
203-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
204            <meta-data
204-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
205                android:name="androidx.emoji2.text.EmojiCompatInitializer"
205-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
206                android:value="androidx.startup" />
206-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
207            <meta-data
207-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
208                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
208-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
209                android:value="androidx.startup" />
209-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
210            <meta-data
210-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
211                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
211-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
212                android:value="androidx.startup" />
212-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
213        </provider>
214
215        <uses-library
215-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
216            android:name="androidx.window.extensions"
216-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
217            android:required="false" />
217-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
218        <uses-library
218-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
219            android:name="androidx.window.sidecar"
219-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
220            android:required="false" />
220-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
221
222        <provider
222-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:8:9-12:43
223            android:name="me.wcy.router.RouterProvider"
223-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:9:13-56
224            android:authorities="me.wcy.music.crouter.provider"
224-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:10:13-68
225            android:exported="false"
225-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:11:13-37
226            android:multiprocess="true" />
226-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:12:13-40
227
228        <receiver
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
229            android:name="androidx.profileinstaller.ProfileInstallReceiver"
229-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
230            android:directBootAware="false"
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
231            android:enabled="true"
231-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
232            android:exported="true"
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
233            android:permission="android.permission.DUMP" >
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
234            <intent-filter>
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
235                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
236            </intent-filter>
237            <intent-filter>
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
238                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
239            </intent-filter>
240            <intent-filter>
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
241                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
242            </intent-filter>
243            <intent-filter>
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
244                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
245            </intent-filter>
246        </receiver>
247
248        <provider
248-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:10:9-14:43
249            android:name="com.qw.soul.permission.InitProvider"
249-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:11:13-63
250            android:authorities="me.wcy.music.permission.provider"
250-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:12:13-71
251            android:exported="false"
251-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:13:13-37
252            android:multiprocess="true" />
252-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:14:13-40
253
254        <service android:name="com.liulishuo.filedownloader.services.FileDownloadService$SharedMainProcessService" />
254-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:12:9-118
254-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:12:18-115
255        <service
255-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:13:9-15:49
256            android:name="com.liulishuo.filedownloader.services.FileDownloadService$SeparateProcessService"
256-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:14:13-108
257            android:process=":filedownloader" />
257-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:15:13-46
258    </application>
259
260</manifest>
