1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="me.wcy.music"
4    android:versionCode="2030001"
5    android:versionName="2.3.0-beta01" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="36" />
10
11    <!-- 基础权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:9:5-81
15-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:9:22-78
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
16-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:10:5-77
16-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:10:22-74
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
17-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:11:5-92
17-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:11:22-89
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:12:5-80
18-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:12:22-77
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:13:5-68
19-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:13:22-65
20    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
20-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:14:5-75
20-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:14:22-72
21    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
21-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:15:5-77
21-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:15:22-74
22
23    <!-- Android Automotive 特性声明 -->
24    <uses-feature
24-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:18:5-20:36
25        android:name="android.hardware.type.automotive"
25-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:19:9-56
26        android:required="false" />
26-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:20:9-33
27
28    <!-- 车载音频应用特性 -->
29    <uses-feature
29-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:23:5-25:36
30        android:name="android.software.leanback"
30-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:24:9-49
31        android:required="false" />
31-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:25:9-33
32
33    <uses-permission android:name="android.permission.VIBRATE" />
33-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:8:5-66
33-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:8:22-63
34    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
34-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:11:5-76
34-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:11:22-73
35    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
35-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:13:5-75
35-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:13:22-72
36    <uses-permission android:name="android.permission.CAMERA" />
36-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:14:5-65
36-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:14:22-62
37
38    <queries>
38-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:16:5-20:15
39        <intent>
39-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:17:9-19:18
40            <action android:name="android.intent.action.SEND" />
40-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:18:13-65
40-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:18:21-62
41        </intent>
42        <intent>
42-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:10:9-12:18
43            <action android:name="android.intent.action.MAIN" />
43-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:61:17-69
43-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:61:25-66
44        </intent>
45        <intent>
45-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:13:9-15:18
46            <action android:name="android.intent.action.VIEW" />
46-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:70:17-69
46-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:70:25-66
47        </intent>
48    </queries>
49
50    <uses-feature android:name="android.hardware.camera" />
50-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:13:5-60
50-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:13:19-57
51    <uses-feature android:name="android.hardware.camera.autofocus" />
51-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:14:5-70
51-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:14:19-67
52
53    <uses-permission android:name="android.permission.FLASHLIGHT" />
53-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:16:5-69
53-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:16:22-66
54    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
54-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:20:5-84
54-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:20:22-81
55
56    <permission
56-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
57        android:name="me.wcy.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
57-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
58        android:protectionLevel="signature" />
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
59
60    <uses-permission android:name="me.wcy.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
60-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
60-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
61
62    <application
62-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:27:5-88:19
63        android:name="me.wcy.music.MusicApplication"
63-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:28:9-41
64        android:allowBackup="true"
64-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:29:9-35
65        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
65-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
66        android:debuggable="true"
67        android:extractNativeLibs="true"
68        android:icon="@drawable/ic_launcher"
68-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:30:9-45
69        android:label="@string/app_name"
69-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:31:9-41
70        android:networkSecurityConfig="@xml/network_security_config"
70-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:32:9-69
71        android:roundIcon="@drawable/ic_launcher_round"
71-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:33:9-56
72        android:supportsRtl="true"
72-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:26:9-35
73        android:testOnly="true"
74        android:theme="@style/AppTheme" >
74-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:34:9-40
75        <service
75-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:36:9-43:19
76            android:name="me.wcy.music.service.MusicService"
76-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:37:13-49
77            android:exported="true"
77-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:38:13-36
78            android:foregroundServiceType="mediaPlayback" >
78-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:39:13-58
79            <intent-filter>
79-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:40:13-42:29
80                <action android:name="androidx.media3.session.MediaSessionService" />
80-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:41:17-86
80-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:41:25-83
81            </intent-filter>
82        </service>
83
84        <receiver
84-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:45:9-51:20
85            android:name="me.wcy.music.download.DownloadReceiver"
85-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:46:13-54
86            android:exported="true" >
86-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:47:13-36
87            <intent-filter>
87-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:48:13-50:29
88                <action android:name="android.intent.action.DOWNLOAD_COMPLETE" />
88-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:49:17-82
88-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:49:25-79
89            </intent-filter>
90        </receiver>
91
92        <activity
92-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:53:9-74:20
93            android:name="me.wcy.music.main.MainActivity"
93-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:54:13-46
94            android:configChanges="orientation|screenSize|keyboardHidden"
94-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:59:13-74
95            android:exported="true"
95-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:55:13-36
96            android:label="@string/app_name"
96-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:56:13-45
97            android:launchMode="singleTop"
97-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:57:13-43
98            android:screenOrientation="landscape" >
98-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:58:13-50
99            <intent-filter>
99-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:60:13-66:29
100                <action android:name="android.intent.action.MAIN" />
100-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:61:17-69
100-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:61:25-66
101
102                <category android:name="android.intent.category.LAUNCHER" />
102-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:63:17-77
102-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:63:27-74
103                <!-- Android Automotive 车载应用分类 -->
104                <category android:name="android.intent.category.CAR_LAUNCHER" />
104-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:65:17-81
104-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:65:27-78
105            </intent-filter>
106
107            <!-- 媒体播放器意图过滤器 -->
108            <intent-filter>
108-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:69:13-73:29
109                <action android:name="android.intent.action.VIEW" />
109-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:70:17-69
109-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:70:25-66
110
111                <category android:name="android.intent.category.DEFAULT" />
111-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:71:17-76
111-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:71:27-73
112
113                <data android:mimeType="audio/*" />
113-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:72:17-52
113-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:72:23-49
114            </intent-filter>
115        </activity>
116        <activity android:name="me.wcy.music.common.MusicFragmentContainerActivity" />
116-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:75:9-75
116-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:75:19-72
117        <activity
117-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:76:9-78:52
118            android:name="me.wcy.music.main.SettingsActivity"
118-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:77:13-50
119            android:label="@string/menu_setting" />
119-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:78:13-49
120        <activity
120-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:79:9-81:50
121            android:name="me.wcy.music.main.AboutActivity"
121-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:80:13-47
122            android:label="@string/menu_about" />
122-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:81:13-47
123        <activity
123-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:82:9-87:53
124            android:name="me.wcy.music.main.playing.PlayingActivity"
124-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:83:13-69
125            android:configChanges="orientation|screenSize|keyboardHidden"
125-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:86:13-74
126            android:launchMode="singleTop"
126-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:84:13-43
127            android:screenOrientation="landscape"
127-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:85:13-50
128            android:theme="@style/AppTheme.Popup" />
128-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:87:13-50
129        <activity
129-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:23:9-25:50
130            android:name="top.wangchenyan.common.ui.activity.FragmentContainerActivity"
130-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:24:13-88
131            android:screenOrientation="behind" />
131-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:25:13-47
132
133        <meta-data
133-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:11:9-13:36
134            android:name="android.notch_support"
134-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:12:13-49
135            android:value="true" /> <!-- PermissonActivity -->
135-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:13:13-33
136        <activity
136-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:15:9-17:75
137            android:name="com.lxj.xpopup.util.XPermission$PermissionActivity"
137-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:16:13-78
138            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
138-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:17:13-72
139
140        <provider
140-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:27:9-35:20
141            android:name="cn.bertsir.zbar.utils.QrFileProvider"
141-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:28:13-64
142            android:authorities="me.wcy.music.zbar.FileProvider"
142-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:29:13-69
143            android:exported="false"
143-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:30:13-37
144            android:grantUriPermissions="true" >
144-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:31:13-47
145            <meta-data
145-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:32:13-34:57
146                android:name="android.support.FILE_PROVIDER_PATHS"
146-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:33:17-67
147                android:resource="@xml/qr_file_paths" />
147-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:34:17-54
148        </provider>
149
150        <activity
150-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:37:9-39:77
151            android:name="cn.bertsir.zbar.QRActivity"
151-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:38:13-54
152            android:configChanges="keyboardHidden|orientation|screenSize" />
152-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:39:13-74
153        <activity
153-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:40:9-42:58
154            android:name="cn.bertsir.zbar.utils.PermissionUtils$PermissionActivity"
154-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:41:13-84
155            android:theme="@style/ActivityTranslucent" />
155-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:42:13-55
156        <activity android:name="com.soundcloud.android.crop.CropImageActivity" />
156-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:43:9-82
156-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:43:19-79
157        <activity
157-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:19:9-24:75
158            android:name="com.blankj.utilcode.util.UtilsTransActivity4MainProcess"
158-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:20:13-83
159            android:configChanges="orientation|keyboardHidden|screenSize"
159-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:21:13-74
160            android:exported="false"
160-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:22:13-37
161            android:theme="@style/ActivityTranslucent"
161-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:23:13-55
162            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
162-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:24:13-72
163        <activity
163-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:25:9-31:75
164            android:name="com.blankj.utilcode.util.UtilsTransActivity"
164-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:26:13-71
165            android:configChanges="orientation|keyboardHidden|screenSize"
165-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:27:13-74
166            android:exported="false"
166-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:28:13-37
167            android:multiprocess="true"
167-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:29:13-40
168            android:theme="@style/ActivityTranslucent"
168-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:30:13-55
169            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
169-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:31:13-72
170
171        <provider
171-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:33:9-41:20
172            android:name="com.blankj.utilcode.util.UtilsFileProvider"
172-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:34:13-70
173            android:authorities="me.wcy.music.utilcode.fileprovider"
173-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:35:13-73
174            android:exported="false"
174-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:36:13-37
175            android:grantUriPermissions="true" >
175-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:37:13-47
176            <meta-data
176-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:32:13-34:57
177                android:name="android.support.FILE_PROVIDER_PATHS"
177-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:33:17-67
178                android:resource="@xml/util_code_provider_paths" />
178-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:34:17-54
179        </provider>
180
181        <service
181-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:43:9-49:19
182            android:name="com.blankj.utilcode.util.MessengerUtils$ServerService"
182-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:44:13-81
183            android:exported="false" >
183-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:45:13-37
184            <intent-filter>
184-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:46:13-48:29
185                <action android:name="me.wcy.music.messenger" />
185-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:17-69
185-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:25-66
186            </intent-filter>
187        </service>
188        <service
188-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
189            android:name="androidx.room.MultiInstanceInvalidationService"
189-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
190            android:directBootAware="true"
190-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
191            android:exported="false" />
191-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
192
193        <provider
193-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
194            android:name="androidx.startup.InitializationProvider"
194-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
195            android:authorities="me.wcy.music.androidx-startup"
195-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
196            android:exported="false" >
196-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
197            <meta-data
197-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
198                android:name="androidx.emoji2.text.EmojiCompatInitializer"
198-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
199                android:value="androidx.startup" />
199-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
200            <meta-data
200-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
201                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
201-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
202                android:value="androidx.startup" />
202-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
203            <meta-data
203-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
204                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
204-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
205                android:value="androidx.startup" />
205-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
206        </provider>
207
208        <uses-library
208-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
209            android:name="androidx.window.extensions"
209-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
210            android:required="false" />
210-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
211        <uses-library
211-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
212            android:name="androidx.window.sidecar"
212-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
213            android:required="false" />
213-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
214
215        <provider
215-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:8:9-12:43
216            android:name="me.wcy.router.RouterProvider"
216-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:9:13-56
217            android:authorities="me.wcy.music.crouter.provider"
217-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:10:13-68
218            android:exported="false"
218-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:11:13-37
219            android:multiprocess="true" />
219-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:12:13-40
220
221        <receiver
221-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
222            android:name="androidx.profileinstaller.ProfileInstallReceiver"
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
223            android:directBootAware="false"
223-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
224            android:enabled="true"
224-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
225            android:exported="true"
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
226            android:permission="android.permission.DUMP" >
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
227            <intent-filter>
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
228                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
229            </intent-filter>
230            <intent-filter>
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
231                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
231-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
231-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
232            </intent-filter>
233            <intent-filter>
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
234                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
235            </intent-filter>
236            <intent-filter>
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
237                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
238            </intent-filter>
239        </receiver>
240
241        <provider
241-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:10:9-14:43
242            android:name="com.qw.soul.permission.InitProvider"
242-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:11:13-63
243            android:authorities="me.wcy.music.permission.provider"
243-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:12:13-71
244            android:exported="false"
244-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:13:13-37
245            android:multiprocess="true" />
245-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:14:13-40
246
247        <service android:name="com.liulishuo.filedownloader.services.FileDownloadService$SharedMainProcessService" />
247-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:12:9-118
247-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:12:18-115
248        <service
248-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:13:9-15:49
249            android:name="com.liulishuo.filedownloader.services.FileDownloadService$SeparateProcessService"
249-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:14:13-108
250            android:process=":filedownloader" />
250-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:15:13-46
251    </application>
252
253</manifest>
