1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="me.wcy.music"
4    android:versionCode="2030001"
5    android:versionName="2.3.0-beta01" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="36" />
10
11    <!-- 基础权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:9:5-81
15-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:9:22-78
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
16-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:10:5-77
16-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:10:22-74
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
17-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:11:5-92
17-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:11:22-89
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:12:5-80
18-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:12:22-77
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:13:5-68
19-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:13:22-65
20    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
20-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:14:5-75
20-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:14:22-72
21    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
21-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:15:5-77
21-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:15:22-74
22
23    <!-- Android Automotive 特性声明 -->
24    <uses-feature
24-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:18:5-20:36
25        android:name="android.hardware.type.automotive"
25-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:19:9-56
26        android:required="false" />
26-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:20:9-33
27
28    <!-- 车载音频应用特性 -->
29    <uses-feature
29-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:23:5-25:36
30        android:name="android.software.leanback"
30-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:24:9-49
31        android:required="false" />
31-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:25:9-33
32
33    <uses-permission android:name="android.permission.VIBRATE" />
33-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:8:5-66
33-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:8:22-63
34    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
34-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:11:5-76
34-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:11:22-73
35    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
35-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:13:5-75
35-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:13:22-72
36    <uses-permission android:name="android.permission.CAMERA" />
36-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:14:5-65
36-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:14:22-62
37
38    <queries>
38-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:16:5-20:15
39        <intent>
39-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:17:9-19:18
40            <action android:name="android.intent.action.SEND" />
40-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:18:13-65
40-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:18:21-62
41        </intent>
42        <intent>
42-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:10:9-12:18
43            <action android:name="android.intent.action.MAIN" />
43-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:61:17-69
43-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:61:25-66
44        </intent>
45        <intent>
45-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:13:9-15:18
46            <action android:name="android.intent.action.VIEW" />
46-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:70:17-69
46-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:70:25-66
47        </intent>
48    </queries>
49
50    <uses-feature android:name="android.hardware.camera" />
50-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:13:5-60
50-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:13:19-57
51    <uses-feature android:name="android.hardware.camera.autofocus" />
51-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:14:5-70
51-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:14:19-67
52
53    <uses-permission android:name="android.permission.FLASHLIGHT" />
53-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:16:5-69
53-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:16:22-66
54    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
54-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:20:5-84
54-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:20:22-81
55
56    <permission
56-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
57        android:name="me.wcy.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
57-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
58        android:protectionLevel="signature" />
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
59
60    <uses-permission android:name="me.wcy.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
60-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
60-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
61
62    <application
62-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:27:5-88:19
63        android:name="me.wcy.music.MusicApplication"
63-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:28:9-41
64        android:allowBackup="true"
64-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:29:9-35
65        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
65-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
66        android:debuggable="true"
67        android:extractNativeLibs="true"
68        android:icon="@drawable/ic_launcher"
68-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:30:9-45
69        android:label="@string/app_name"
69-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:31:9-41
70        android:networkSecurityConfig="@xml/network_security_config"
70-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:32:9-69
71        android:roundIcon="@drawable/ic_launcher_round"
71-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:33:9-56
72        android:supportsRtl="true"
72-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:26:9-35
73        android:theme="@style/AppTheme" >
73-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:34:9-40
74        <service
74-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:36:9-43:19
75            android:name="me.wcy.music.service.MusicService"
75-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:37:13-49
76            android:exported="true"
76-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:38:13-36
77            android:foregroundServiceType="mediaPlayback" >
77-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:39:13-58
78            <intent-filter>
78-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:40:13-42:29
79                <action android:name="androidx.media3.session.MediaSessionService" />
79-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:41:17-86
79-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:41:25-83
80            </intent-filter>
81        </service>
82
83        <receiver
83-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:45:9-51:20
84            android:name="me.wcy.music.download.DownloadReceiver"
84-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:46:13-54
85            android:exported="true" >
85-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:47:13-36
86            <intent-filter>
86-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:48:13-50:29
87                <action android:name="android.intent.action.DOWNLOAD_COMPLETE" />
87-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:49:17-82
87-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:49:25-79
88            </intent-filter>
89        </receiver>
90
91        <activity
91-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:53:9-74:20
92            android:name="me.wcy.music.main.MainActivity"
92-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:54:13-46
93            android:configChanges="orientation|screenSize|keyboardHidden"
93-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:59:13-74
94            android:exported="true"
94-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:55:13-36
95            android:label="@string/app_name"
95-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:56:13-45
96            android:launchMode="singleTop"
96-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:57:13-43
97            android:screenOrientation="landscape" >
97-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:58:13-50
98            <intent-filter>
98-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:60:13-66:29
99                <action android:name="android.intent.action.MAIN" />
99-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:61:17-69
99-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:61:25-66
100
101                <category android:name="android.intent.category.LAUNCHER" />
101-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:63:17-77
101-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:63:27-74
102                <!-- Android Automotive 车载应用分类 -->
103                <category android:name="android.intent.category.CAR_LAUNCHER" />
103-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:65:17-81
103-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:65:27-78
104            </intent-filter>
105
106            <!-- 媒体播放器意图过滤器 -->
107            <intent-filter>
107-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:69:13-73:29
108                <action android:name="android.intent.action.VIEW" />
108-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:70:17-69
108-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:70:25-66
109
110                <category android:name="android.intent.category.DEFAULT" />
110-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:71:17-76
110-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:71:27-73
111
112                <data android:mimeType="audio/*" />
112-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:72:17-52
112-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:72:23-49
113            </intent-filter>
114        </activity>
115        <activity android:name="me.wcy.music.common.MusicFragmentContainerActivity" />
115-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:75:9-75
115-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:75:19-72
116        <activity
116-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:76:9-78:52
117            android:name="me.wcy.music.main.SettingsActivity"
117-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:77:13-50
118            android:label="@string/menu_setting" />
118-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:78:13-49
119        <activity
119-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:79:9-81:50
120            android:name="me.wcy.music.main.AboutActivity"
120-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:80:13-47
121            android:label="@string/menu_about" />
121-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:81:13-47
122        <activity
122-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:82:9-87:53
123            android:name="me.wcy.music.main.playing.PlayingActivity"
123-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:83:13-69
124            android:configChanges="orientation|screenSize|keyboardHidden"
124-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:86:13-74
125            android:launchMode="singleTop"
125-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:84:13-43
126            android:screenOrientation="landscape"
126-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:85:13-50
127            android:theme="@style/AppTheme.Popup" />
127-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:87:13-50
128        <activity
128-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:23:9-25:50
129            android:name="top.wangchenyan.common.ui.activity.FragmentContainerActivity"
129-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:24:13-88
130            android:screenOrientation="behind" />
130-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:25:13-47
131
132        <meta-data
132-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:11:9-13:36
133            android:name="android.notch_support"
133-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:12:13-49
134            android:value="true" /> <!-- PermissonActivity -->
134-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:13:13-33
135        <activity
135-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:15:9-17:75
136            android:name="com.lxj.xpopup.util.XPermission$PermissionActivity"
136-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:16:13-78
137            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
137-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:17:13-72
138
139        <provider
139-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:27:9-35:20
140            android:name="cn.bertsir.zbar.utils.QrFileProvider"
140-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:28:13-64
141            android:authorities="me.wcy.music.zbar.FileProvider"
141-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:29:13-69
142            android:exported="false"
142-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:30:13-37
143            android:grantUriPermissions="true" >
143-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:31:13-47
144            <meta-data
144-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:32:13-34:57
145                android:name="android.support.FILE_PROVIDER_PATHS"
145-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:33:17-67
146                android:resource="@xml/qr_file_paths" />
146-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:34:17-54
147        </provider>
148
149        <activity
149-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:37:9-39:77
150            android:name="cn.bertsir.zbar.QRActivity"
150-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:38:13-54
151            android:configChanges="keyboardHidden|orientation|screenSize" />
151-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:39:13-74
152        <activity
152-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:40:9-42:58
153            android:name="cn.bertsir.zbar.utils.PermissionUtils$PermissionActivity"
153-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:41:13-84
154            android:theme="@style/ActivityTranslucent" />
154-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:42:13-55
155        <activity android:name="com.soundcloud.android.crop.CropImageActivity" />
155-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:43:9-82
155-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:43:19-79
156        <activity
156-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:19:9-24:75
157            android:name="com.blankj.utilcode.util.UtilsTransActivity4MainProcess"
157-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:20:13-83
158            android:configChanges="orientation|keyboardHidden|screenSize"
158-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:21:13-74
159            android:exported="false"
159-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:22:13-37
160            android:theme="@style/ActivityTranslucent"
160-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:23:13-55
161            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
161-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:24:13-72
162        <activity
162-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:25:9-31:75
163            android:name="com.blankj.utilcode.util.UtilsTransActivity"
163-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:26:13-71
164            android:configChanges="orientation|keyboardHidden|screenSize"
164-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:27:13-74
165            android:exported="false"
165-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:28:13-37
166            android:multiprocess="true"
166-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:29:13-40
167            android:theme="@style/ActivityTranslucent"
167-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:30:13-55
168            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
168-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:31:13-72
169
170        <provider
170-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:33:9-41:20
171            android:name="com.blankj.utilcode.util.UtilsFileProvider"
171-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:34:13-70
172            android:authorities="me.wcy.music.utilcode.fileprovider"
172-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:35:13-73
173            android:exported="false"
173-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:36:13-37
174            android:grantUriPermissions="true" >
174-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:37:13-47
175            <meta-data
175-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:32:13-34:57
176                android:name="android.support.FILE_PROVIDER_PATHS"
176-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:33:17-67
177                android:resource="@xml/util_code_provider_paths" />
177-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:34:17-54
178        </provider>
179
180        <service
180-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:43:9-49:19
181            android:name="com.blankj.utilcode.util.MessengerUtils$ServerService"
181-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:44:13-81
182            android:exported="false" >
182-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:45:13-37
183            <intent-filter>
183-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:46:13-48:29
184                <action android:name="me.wcy.music.messenger" />
184-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:17-69
184-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:25-66
185            </intent-filter>
186        </service>
187        <service
187-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
188            android:name="androidx.room.MultiInstanceInvalidationService"
188-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
189            android:directBootAware="true"
189-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
190            android:exported="false" />
190-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
191
192        <provider
192-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
193            android:name="androidx.startup.InitializationProvider"
193-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
194            android:authorities="me.wcy.music.androidx-startup"
194-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
195            android:exported="false" >
195-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
196            <meta-data
196-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
197                android:name="androidx.emoji2.text.EmojiCompatInitializer"
197-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
198                android:value="androidx.startup" />
198-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
199            <meta-data
199-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
200                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
200-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
201                android:value="androidx.startup" />
201-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
202            <meta-data
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
203                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
203-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
204                android:value="androidx.startup" />
204-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
205        </provider>
206
207        <uses-library
207-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
208            android:name="androidx.window.extensions"
208-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
209            android:required="false" />
209-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
210        <uses-library
210-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
211            android:name="androidx.window.sidecar"
211-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
212            android:required="false" />
212-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
213
214        <provider
214-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:8:9-12:43
215            android:name="me.wcy.router.RouterProvider"
215-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:9:13-56
216            android:authorities="me.wcy.music.crouter.provider"
216-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:10:13-68
217            android:exported="false"
217-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:11:13-37
218            android:multiprocess="true" />
218-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:12:13-40
219
220        <receiver
220-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
221            android:name="androidx.profileinstaller.ProfileInstallReceiver"
221-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
222            android:directBootAware="false"
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
223            android:enabled="true"
223-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
224            android:exported="true"
224-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
225            android:permission="android.permission.DUMP" >
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
226            <intent-filter>
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
227                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
228            </intent-filter>
229            <intent-filter>
229-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
230                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
231            </intent-filter>
232            <intent-filter>
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
233                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
234            </intent-filter>
235            <intent-filter>
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
236                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
237            </intent-filter>
238        </receiver>
239
240        <provider
240-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:10:9-14:43
241            android:name="com.qw.soul.permission.InitProvider"
241-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:11:13-63
242            android:authorities="me.wcy.music.permission.provider"
242-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:12:13-71
243            android:exported="false"
243-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:13:13-37
244            android:multiprocess="true" />
244-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:14:13-40
245
246        <service android:name="com.liulishuo.filedownloader.services.FileDownloadService$SharedMainProcessService" />
246-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:12:9-118
246-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:12:18-115
247        <service
247-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:13:9-15:49
248            android:name="com.liulishuo.filedownloader.services.FileDownloadService$SeparateProcessService"
248-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:14:13-108
249            android:process=":filedownloader" />
249-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:15:13-46
250    </application>
251
252</manifest>
