-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:2:1-90:12
INJECTED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:2:1-90:12
INJECTED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:2:1-90:12
INJECTED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:2:1-90:12
MERGED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:2:1-28:12
MERGED from [com.github.wangchenyan:radapter3:3.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\b622df0a4bcc713688ff314c6d6cb1fd\transformed\jetified-radapter3-3.1.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\d38f102b94af1564cb8ab3597da658e2\transformed\jetified-viewbinding-8.5.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:2:1-20:12
MERGED from [com.github.getActivity:ShapeView:9.2] C:\Users\<USER>\.gradle\caches\transforms-4\6bc7bdbae5cf6cc8646b23d8284d3bd6\transformed\jetified-ShapeView-9.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ed9553fb9af75b90a85668bb0aac2cdb\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.kingja.loadsir:loadsir:1.3.8] C:\Users\<USER>\.gradle\caches\transforms-4\1268301a2f3b09de4056b7e6a84dabb6\transformed\jetified-loadsir-1.3.8\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\bb2e1878f386330507e4686f33b611fc\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:2:1-46:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\79e6fcc9ace287bcd7854160e408434b\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\aab301834a75791e1f2e19885ae0c2c4\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\40b0160c565c3675e15a0634390e90ad\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:2:1-52:12
MERGED from [com.github.li-xiaojun:EasyAdapter:1.2.5] C:\Users\<USER>\.gradle\caches\transforms-4\694b3014be6d581a3b44ce8794b4d0ae\transformed\jetified-EasyAdapter-1.2.5\AndroidManifest.xml:2:1-11:12
MERGED from [com.davemorrissey.labs:subsampling-scale-image-view-androidx:3.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\0db8959781bab9a2fcd24c26450d417f\transformed\jetified-subsampling-scale-image-view-androidx-3.10.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\8110c868e8d28496c81ffd9e11046d18\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\028c157f0b2d69783b7a591a86ffb5f5\transformed\jetified-flexbox-3.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\aa4fb84626d5f6de59defa3ecf30421a\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:hilt-android:2.48.1] C:\Users\<USER>\.gradle\caches\transforms-4\78525c492b56765544713dd2de6e2e56\transformed\jetified-hilt-android-2.48.1\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\578cfbc226410fe228090d85cc742b1c\transformed\jetified-media3-datasource-okhttp-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-session:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\31112261c7df96d69093b9be7b3f632a\transformed\jetified-media3-session-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\286503f715fac5b6668bc7afcac59c2c\transformed\jetified-media3-ui-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\f9709f4606e5c01785c7805267918afc\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\281e349d24abfd9bae79f5d6c3324d55\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\d9d19a0db6e0edf218fc9576eeb6fd94\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\ecdf7f4069f16d483140d34015819110\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\0d8f906926c176156215c853321b4d5b\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\34cb68c8c40b7ac8645b31f2ae8dcfc9\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\be9ab8b566fc36ab0e145ee170d21c48\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\42c389001b87fda877b3a0088804c131\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:glide:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-4\03053a03e9ddba62a0301866c54445d1\transformed\jetified-glide-4.13.2\AndroidManifest.xml:2:1-12:12
MERGED from [androidx.fragment:fragment:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a449e64b68ca8bd907f4afbcb5af67e\transformed\fragment-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\6368d21203a81dc13da80468eefc39dd\transformed\jetified-fragment-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d44574603da265a6c1b256fff6662e77\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\928c26eead87a94acde540632d1bfc7b\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\e31657dd34d6bf5aeacc84276cb0a8aa\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\f28be1469b591ac2a1bfdade0af99452\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\5f908a77d274f961dadccaeac84b96f2\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\b68847b0a411010df40532f52c2037e4\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\7aa8c77380a5ed16c6656b57918ab590\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\0497206f8e4bd6a6db8ca27bc9651f08\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\d3a22ab5883023d726e62d1d43b7d8fa\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d8e91550195c2290c773310adc9dcd6d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\79ac80900eb8e304fc338e5da23a5c74\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b7a9c134defa082956e640ec4573afc1\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\1c4246c505b46cbdc020c2d50f3e1a33\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0adad66e19b0a74a9c521099539ea00f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\4532f8be91e72d4dc1b48e5d4227eb36\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d71f70c69215f577a90944bf6a3e368d\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\47cb18e28a27b7ac594486d70793fc5b\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\aa7e8e9b663d441e550fdf985a33e4ca\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d63661f1db6afb69a8ddee8ba07d5fe\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\02c111dcc5fc2e75d9b8ce6a1344ce62\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\a042e49ab735f9138e63d64048775061\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\ed2237fd35b86541579a9fe40398766c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\53217a327f68cdeb0e3c5518f7eb6563\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd9bac64c612fb6471dfc66639e1a081\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a3db9ade18afc32ea942e1ba9669c0b9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\e785031438cf12193cad78a18e9eddad\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a94bb33d47f62ba30873cc56f2a1ac4f\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\39ac9eaa3a0fe9188a12699c6eccc2b4\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:2:1-15:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d4d22deb278696b3acd2f5563155bb8\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\da09a122e62a01c51dce5c0979a48f6a\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\f6d4d1cfc947630d3dbe9b9bd9cb33e7\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\014e37feb492f82144595b0aeb1743e9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0b2afb2fb322d28cee364275a6f0485\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1e36f4c2b35e3470b11041a2c7be2f7e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\43774fb6f95b464a4ea7633de63a331f\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f59d275c7ea939364ca01fc022c2c823\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\99ca1d9bdcab120c686037f192b4447f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5f174e0f9486b759d5529921362fdfc1\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\5391e182f50d9829e51a36ebfd130f85\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-4\ded48ab71ed0b32f86134b25e6d8766a\transformed\jetified-gifdecoder-4.13.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c059e6efa9f54786295892fc1cf433b2\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9b19fa4527916ebf3d15cfdc7db89f60\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b83b0d390b5c6d939e688319f40afb3\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.wangchenyan:lrcview:2.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\6b30b950621389767111d64e2aa84c72\transformed\jetified-lrcview-2.2.2\AndroidManifest.xml:2:1-10:12
MERGED from [jp.wasabeef:blurry:4.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\87cfde7eab665a366a2d04fc11a5d3d6\transformed\jetified-blurry-4.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [io.github.youth5201314:banner:2.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\04a87785ac18bd7fcb423a6ffb4641b3\transformed\jetified-banner-2.2.2\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48.1] C:\Users\<USER>\.gradle\caches\transforms-4\701193d5ac9cb0968b1d6de331ddf545\transformed\jetified-dagger-lint-aar-2.48.1\AndroidManifest.xml:16:1-19:12
MERGED from [com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:2:1-18:12
MERGED from [top.zibin:Luban:1.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\d237c14f6eae51f86c73b8a2260942eb\transformed\jetified-Luban-1.1.8\AndroidManifest.xml:2:1-11:12
MERGED from [com.elvishew:xlog:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\19370f86277ef133806d5c76d004537f\transformed\jetified-xlog-1.10.1\AndroidManifest.xml:2:1-9:12
MERGED from [io.github.scwang90:refresh-layout-kernel:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-4\bb5eb680fc11b2e9b49beb6be8b1f622\transformed\jetified-refresh-layout-kernel-2.0.5\AndroidManifest.xml:2:1-11:12
MERGED from [io.github.scwang90:refresh-header-material:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-4\7e0bde443a67696235ff6f7dde308f5e\transformed\jetified-refresh-header-material-2.0.5\AndroidManifest.xml:2:1-11:12
MERGED from [io.github.scwang90:refresh-footer-classics:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-4\f32dec6a4022a9a504ce4323317954bd\transformed\jetified-refresh-footer-classics-2.0.5\AndroidManifest.xml:2:1-11:12
MERGED from [com.soundcloud.android:android-crop:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\e14d147c3d78b264e53fe39d2725f464\transformed\jetified-android-crop-1.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.github.getActivity:ShapeDrawable:3.2] C:\Users\<USER>\.gradle\caches\transforms-4\872814dde57cc26585340a57c712db13\transformed\jetified-ShapeDrawable-3.2\AndroidManifest.xml:2:1-9:12
MERGED from [io.github.scwang90:refresh-drawable-paint:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-4\1d78fa585898a65b2b2237096416fca7\transformed\jetified-refresh-drawable-paint-2.0.5\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:17:5-67
MERGED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:17:5-67
MERGED from [com.github.wangchenyan:lrcview:2.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\6b30b950621389767111d64e2aa84c72\transformed\jetified-lrcview-2.2.2\AndroidManifest.xml:7:5-67
MERGED from [com.github.wangchenyan:lrcview:2.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\6b30b950621389767111d64e2aa84c72\transformed\jetified-lrcview-2.2.2\AndroidManifest.xml:7:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:7:5-79
MERGED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:7:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\34cb68c8c40b7ac8645b31f2ae8dcfc9\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\34cb68c8c40b7ac8645b31f2ae8dcfc9\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\be9ab8b566fc36ab0e145ee170d21c48\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\be9ab8b566fc36ab0e145ee170d21c48\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:9:5-81
MERGED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:10:5-81
MERGED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:10:5-81
MERGED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:18:5-81
MERGED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:18:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:10:5-77
MERGED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:9:5-77
MERGED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:9:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:10:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:11:5-92
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:11:22-89
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:12:5-80
MERGED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:9:5-80
MERGED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:9:5-80
MERGED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:19:5-80
MERGED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:19:5-80
MERGED from [com.github.wangchenyan:lrcview:2.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\6b30b950621389767111d64e2aa84c72\transformed\jetified-lrcview-2.2.2\AndroidManifest.xml:8:5-80
MERGED from [com.github.wangchenyan:lrcview:2.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\6b30b950621389767111d64e2aa84c72\transformed\jetified-lrcview-2.2.2\AndroidManifest.xml:8:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:12:22-77
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:13:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:13:22-65
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:14:5-75
MERGED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:12:5-75
MERGED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:12:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:14:22-72
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:15:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:15:22-74
uses-feature#android.hardware.type.automotive
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:18:5-20:36
	android:required
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:20:9-33
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:19:9-56
uses-feature#android.software.leanback
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:23:5-25:36
	android:required
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:25:9-33
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:24:9-49
application
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:27:5-88:19
INJECTED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:27:5-88:19
MERGED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:22:5-26:19
MERGED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:22:5-26:19
MERGED from [com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:10:5-18:19
MERGED from [com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:10:5-18:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ed9553fb9af75b90a85668bb0aac2cdb\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ed9553fb9af75b90a85668bb0aac2cdb\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\bb2e1878f386330507e4686f33b611fc\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\bb2e1878f386330507e4686f33b611fc\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:23:5-44:19
MERGED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:23:5-44:19
MERGED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:18:5-50:19
MERGED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:18:5-50:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [com.github.bumptech.glide:glide:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-4\03053a03e9ddba62a0301866c54445d1\transformed\jetified-glide-4.13.2\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-4\03053a03e9ddba62a0301866c54445d1\transformed\jetified-glide-4.13.2\AndroidManifest.xml:10:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:7:5-13:19
MERGED from [com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:7:5-13:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1e36f4c2b35e3470b11041a2c7be2f7e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1e36f4c2b35e3470b11041a2c7be2f7e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\99ca1d9bdcab120c686037f192b4447f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\99ca1d9bdcab120c686037f192b4447f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-4\ded48ab71ed0b32f86134b25e6d8766a\transformed\jetified-gifdecoder-4.13.2\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-4\ded48ab71ed0b32f86134b25e6d8766a\transformed\jetified-gifdecoder-4.13.2\AndroidManifest.xml:9:5-20
MERGED from [com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:9:5-15:19
MERGED from [com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:9:5-15:19
MERGED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:11:5-16:19
MERGED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:11:5-16:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:26:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:31:9-41
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:33:9-56
	android:icon
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:30:9-45
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:29:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:34:9-40
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:32:9-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:28:9-41
service#me.wcy.music.service.MusicService
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:36:9-43:19
	android:exported
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:38:13-36
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:39:13-58
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:37:13-49
intent-filter#action:name:androidx.media3.session.MediaSessionService
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:40:13-42:29
action#androidx.media3.session.MediaSessionService
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:41:17-86
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:41:25-83
receiver#me.wcy.music.download.DownloadReceiver
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:45:9-51:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:47:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:46:13-54
intent-filter#action:name:android.intent.action.DOWNLOAD_COMPLETE
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:48:13-50:29
action#android.intent.action.DOWNLOAD_COMPLETE
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:49:17-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:49:25-79
activity#me.wcy.music.main.MainActivity
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:53:9-74:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:58:13-50
	android:label
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:56:13-45
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:57:13-43
	android:exported
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:55:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:59:13-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:54:13-46
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.CAR_LAUNCHER+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:60:13-66:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:61:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:61:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:63:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:63:27-74
category#android.intent.category.CAR_LAUNCHER
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:65:17-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:65:27-78
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.DEFAULT+data:mimeType:audio/*
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:69:13-73:29
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:70:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:70:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:71:17-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:71:27-73
data
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:72:17-52
	android:mimeType
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:72:23-49
activity#me.wcy.music.common.MusicFragmentContainerActivity
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:75:9-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:75:19-72
activity#me.wcy.music.main.SettingsActivity
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:76:9-78:52
	android:label
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:78:13-49
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:77:13-50
activity#me.wcy.music.main.AboutActivity
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:79:9-81:50
	android:label
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:81:13-47
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:80:13-47
activity#me.wcy.music.main.playing.PlayingActivity
ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:82:9-87:53
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:85:13-50
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:84:13-43
	android:configChanges
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:86:13-74
	android:theme
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:87:13-50
	android:name
		ADDED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:83:13-69
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml
MERGED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.wangchenyan:radapter3:3.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\b622df0a4bcc713688ff314c6d6cb1fd\transformed\jetified-radapter3-3.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.wangchenyan:radapter3:3.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\b622df0a4bcc713688ff314c6d6cb1fd\transformed\jetified-radapter3-3.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\d38f102b94af1564cb8ab3597da658e2\transformed\jetified-viewbinding-8.5.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\d38f102b94af1564cb8ab3597da658e2\transformed\jetified-viewbinding-8.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.getActivity:ShapeView:9.2] C:\Users\<USER>\.gradle\caches\transforms-4\6bc7bdbae5cf6cc8646b23d8284d3bd6\transformed\jetified-ShapeView-9.2\AndroidManifest.xml:7:5-44
MERGED from [com.github.getActivity:ShapeView:9.2] C:\Users\<USER>\.gradle\caches\transforms-4\6bc7bdbae5cf6cc8646b23d8284d3bd6\transformed\jetified-ShapeView-9.2\AndroidManifest.xml:7:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ed9553fb9af75b90a85668bb0aac2cdb\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\ed9553fb9af75b90a85668bb0aac2cdb\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.kingja.loadsir:loadsir:1.3.8] C:\Users\<USER>\.gradle\caches\transforms-4\1268301a2f3b09de4056b7e6a84dabb6\transformed\jetified-loadsir-1.3.8\AndroidManifest.xml:7:5-9:41
MERGED from [com.kingja.loadsir:loadsir:1.3.8] C:\Users\<USER>\.gradle\caches\transforms-4\1268301a2f3b09de4056b7e6a84dabb6\transformed\jetified-loadsir-1.3.8\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\bb2e1878f386330507e4686f33b611fc\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\bb2e1878f386330507e4686f33b611fc\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:7:5-9:41
MERGED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\79e6fcc9ace287bcd7854160e408434b\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\79e6fcc9ace287bcd7854160e408434b\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\aab301834a75791e1f2e19885ae0c2c4\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\aab301834a75791e1f2e19885ae0c2c4\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\40b0160c565c3675e15a0634390e90ad\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\40b0160c565c3675e15a0634390e90ad\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:7:5-44
MERGED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:7:5-44
MERGED from [com.github.li-xiaojun:EasyAdapter:1.2.5] C:\Users\<USER>\.gradle\caches\transforms-4\694b3014be6d581a3b44ce8794b4d0ae\transformed\jetified-EasyAdapter-1.2.5\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.li-xiaojun:EasyAdapter:1.2.5] C:\Users\<USER>\.gradle\caches\transforms-4\694b3014be6d581a3b44ce8794b4d0ae\transformed\jetified-EasyAdapter-1.2.5\AndroidManifest.xml:7:5-9:41
MERGED from [com.davemorrissey.labs:subsampling-scale-image-view-androidx:3.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\0db8959781bab9a2fcd24c26450d417f\transformed\jetified-subsampling-scale-image-view-androidx-3.10.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.davemorrissey.labs:subsampling-scale-image-view-androidx:3.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\0db8959781bab9a2fcd24c26450d417f\transformed\jetified-subsampling-scale-image-view-androidx-3.10.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\8110c868e8d28496c81ffd9e11046d18\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\8110c868e8d28496c81ffd9e11046d18\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\028c157f0b2d69783b7a591a86ffb5f5\transformed\jetified-flexbox-3.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\028c157f0b2d69783b7a591a86ffb5f5\transformed\jetified-flexbox-3.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\aa4fb84626d5f6de59defa3ecf30421a\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\aa4fb84626d5f6de59defa3ecf30421a\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.48.1] C:\Users\<USER>\.gradle\caches\transforms-4\78525c492b56765544713dd2de6e2e56\transformed\jetified-hilt-android-2.48.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48.1] C:\Users\<USER>\.gradle\caches\transforms-4\78525c492b56765544713dd2de6e2e56\transformed\jetified-hilt-android-2.48.1\AndroidManifest.xml:18:3-42
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\578cfbc226410fe228090d85cc742b1c\transformed\jetified-media3-datasource-okhttp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\578cfbc226410fe228090d85cc742b1c\transformed\jetified-media3-datasource-okhttp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\31112261c7df96d69093b9be7b3f632a\transformed\jetified-media3-session-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\31112261c7df96d69093b9be7b3f632a\transformed\jetified-media3-session-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\286503f715fac5b6668bc7afcac59c2c\transformed\jetified-media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\286503f715fac5b6668bc7afcac59c2c\transformed\jetified-media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\f9709f4606e5c01785c7805267918afc\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\f9709f4606e5c01785c7805267918afc\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\281e349d24abfd9bae79f5d6c3324d55\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\281e349d24abfd9bae79f5d6c3324d55\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\d9d19a0db6e0edf218fc9576eeb6fd94\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\d9d19a0db6e0edf218fc9576eeb6fd94\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\ecdf7f4069f16d483140d34015819110\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\ecdf7f4069f16d483140d34015819110\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\0d8f906926c176156215c853321b4d5b\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\0d8f906926c176156215c853321b4d5b\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\34cb68c8c40b7ac8645b31f2ae8dcfc9\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\34cb68c8c40b7ac8645b31f2ae8dcfc9\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\be9ab8b566fc36ab0e145ee170d21c48\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\be9ab8b566fc36ab0e145ee170d21c48\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\42c389001b87fda877b3a0088804c131\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\42c389001b87fda877b3a0088804c131\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:glide:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-4\03053a03e9ddba62a0301866c54445d1\transformed\jetified-glide-4.13.2\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-4\03053a03e9ddba62a0301866c54445d1\transformed\jetified-glide-4.13.2\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.fragment:fragment:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a449e64b68ca8bd907f4afbcb5af67e\transformed\fragment-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a449e64b68ca8bd907f4afbcb5af67e\transformed\fragment-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\6368d21203a81dc13da80468eefc39dd\transformed\jetified-fragment-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\6368d21203a81dc13da80468eefc39dd\transformed\jetified-fragment-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d44574603da265a6c1b256fff6662e77\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\d44574603da265a6c1b256fff6662e77\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\928c26eead87a94acde540632d1bfc7b\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\928c26eead87a94acde540632d1bfc7b\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\e31657dd34d6bf5aeacc84276cb0a8aa\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\e31657dd34d6bf5aeacc84276cb0a8aa\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\f28be1469b591ac2a1bfdade0af99452\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\f28be1469b591ac2a1bfdade0af99452\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\5f908a77d274f961dadccaeac84b96f2\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\5f908a77d274f961dadccaeac84b96f2\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\b68847b0a411010df40532f52c2037e4\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\b68847b0a411010df40532f52c2037e4\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\7aa8c77380a5ed16c6656b57918ab590\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\7aa8c77380a5ed16c6656b57918ab590\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\0497206f8e4bd6a6db8ca27bc9651f08\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\0497206f8e4bd6a6db8ca27bc9651f08\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\d3a22ab5883023d726e62d1d43b7d8fa\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\d3a22ab5883023d726e62d1d43b7d8fa\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d8e91550195c2290c773310adc9dcd6d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d8e91550195c2290c773310adc9dcd6d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\79ac80900eb8e304fc338e5da23a5c74\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\79ac80900eb8e304fc338e5da23a5c74\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b7a9c134defa082956e640ec4573afc1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b7a9c134defa082956e640ec4573afc1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\1c4246c505b46cbdc020c2d50f3e1a33\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\1c4246c505b46cbdc020c2d50f3e1a33\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0adad66e19b0a74a9c521099539ea00f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0adad66e19b0a74a9c521099539ea00f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\4532f8be91e72d4dc1b48e5d4227eb36\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\4532f8be91e72d4dc1b48e5d4227eb36\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d71f70c69215f577a90944bf6a3e368d\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d71f70c69215f577a90944bf6a3e368d\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\47cb18e28a27b7ac594486d70793fc5b\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\47cb18e28a27b7ac594486d70793fc5b\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\aa7e8e9b663d441e550fdf985a33e4ca\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\aa7e8e9b663d441e550fdf985a33e4ca\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d63661f1db6afb69a8ddee8ba07d5fe\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d63661f1db6afb69a8ddee8ba07d5fe\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\02c111dcc5fc2e75d9b8ce6a1344ce62\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\02c111dcc5fc2e75d9b8ce6a1344ce62\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\a042e49ab735f9138e63d64048775061\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\a042e49ab735f9138e63d64048775061\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\ed2237fd35b86541579a9fe40398766c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\ed2237fd35b86541579a9fe40398766c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\53217a327f68cdeb0e3c5518f7eb6563\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\53217a327f68cdeb0e3c5518f7eb6563\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd9bac64c612fb6471dfc66639e1a081\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd9bac64c612fb6471dfc66639e1a081\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a3db9ade18afc32ea942e1ba9669c0b9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a3db9ade18afc32ea942e1ba9669c0b9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\e785031438cf12193cad78a18e9eddad\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\e785031438cf12193cad78a18e9eddad\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a94bb33d47f62ba30873cc56f2a1ac4f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a94bb33d47f62ba30873cc56f2a1ac4f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\39ac9eaa3a0fe9188a12699c6eccc2b4\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\39ac9eaa3a0fe9188a12699c6eccc2b4\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d4d22deb278696b3acd2f5563155bb8\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d4d22deb278696b3acd2f5563155bb8\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\da09a122e62a01c51dce5c0979a48f6a\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\da09a122e62a01c51dce5c0979a48f6a\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\f6d4d1cfc947630d3dbe9b9bd9cb33e7\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\f6d4d1cfc947630d3dbe9b9bd9cb33e7\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\014e37feb492f82144595b0aeb1743e9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\014e37feb492f82144595b0aeb1743e9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0b2afb2fb322d28cee364275a6f0485\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0b2afb2fb322d28cee364275a6f0485\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1e36f4c2b35e3470b11041a2c7be2f7e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1e36f4c2b35e3470b11041a2c7be2f7e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\43774fb6f95b464a4ea7633de63a331f\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\43774fb6f95b464a4ea7633de63a331f\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f59d275c7ea939364ca01fc022c2c823\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f59d275c7ea939364ca01fc022c2c823\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\99ca1d9bdcab120c686037f192b4447f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\99ca1d9bdcab120c686037f192b4447f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5f174e0f9486b759d5529921362fdfc1\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5f174e0f9486b759d5529921362fdfc1\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\5391e182f50d9829e51a36ebfd130f85\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\5391e182f50d9829e51a36ebfd130f85\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-4\ded48ab71ed0b32f86134b25e6d8766a\transformed\jetified-gifdecoder-4.13.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.13.2] C:\Users\<USER>\.gradle\caches\transforms-4\ded48ab71ed0b32f86134b25e6d8766a\transformed\jetified-gifdecoder-4.13.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c059e6efa9f54786295892fc1cf433b2\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c059e6efa9f54786295892fc1cf433b2\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9b19fa4527916ebf3d15cfdc7db89f60\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9b19fa4527916ebf3d15cfdc7db89f60\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b83b0d390b5c6d939e688319f40afb3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b83b0d390b5c6d939e688319f40afb3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.wangchenyan:lrcview:2.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\6b30b950621389767111d64e2aa84c72\transformed\jetified-lrcview-2.2.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.wangchenyan:lrcview:2.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\6b30b950621389767111d64e2aa84c72\transformed\jetified-lrcview-2.2.2\AndroidManifest.xml:5:5-44
MERGED from [jp.wasabeef:blurry:4.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\87cfde7eab665a366a2d04fc11a5d3d6\transformed\jetified-blurry-4.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [jp.wasabeef:blurry:4.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\87cfde7eab665a366a2d04fc11a5d3d6\transformed\jetified-blurry-4.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [io.github.youth5201314:banner:2.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\04a87785ac18bd7fcb423a6ffb4641b3\transformed\jetified-banner-2.2.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.youth5201314:banner:2.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\04a87785ac18bd7fcb423a6ffb4641b3\transformed\jetified-banner-2.2.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.dagger:dagger-lint-aar:2.48.1] C:\Users\<USER>\.gradle\caches\transforms-4\701193d5ac9cb0968b1d6de331ddf545\transformed\jetified-dagger-lint-aar-2.48.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48.1] C:\Users\<USER>\.gradle\caches\transforms-4\701193d5ac9cb0968b1d6de331ddf545\transformed\jetified-dagger-lint-aar-2.48.1\AndroidManifest.xml:18:3-42
MERGED from [com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:6:5-43
MERGED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:6:5-43
MERGED from [top.zibin:Luban:1.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\d237c14f6eae51f86c73b8a2260942eb\transformed\jetified-Luban-1.1.8\AndroidManifest.xml:7:5-9:41
MERGED from [top.zibin:Luban:1.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\d237c14f6eae51f86c73b8a2260942eb\transformed\jetified-Luban-1.1.8\AndroidManifest.xml:7:5-9:41
MERGED from [com.elvishew:xlog:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\19370f86277ef133806d5c76d004537f\transformed\jetified-xlog-1.10.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.elvishew:xlog:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\19370f86277ef133806d5c76d004537f\transformed\jetified-xlog-1.10.1\AndroidManifest.xml:5:5-7:41
MERGED from [io.github.scwang90:refresh-layout-kernel:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-4\bb5eb680fc11b2e9b49beb6be8b1f622\transformed\jetified-refresh-layout-kernel-2.0.5\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.scwang90:refresh-layout-kernel:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-4\bb5eb680fc11b2e9b49beb6be8b1f622\transformed\jetified-refresh-layout-kernel-2.0.5\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.scwang90:refresh-header-material:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-4\7e0bde443a67696235ff6f7dde308f5e\transformed\jetified-refresh-header-material-2.0.5\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.scwang90:refresh-header-material:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-4\7e0bde443a67696235ff6f7dde308f5e\transformed\jetified-refresh-header-material-2.0.5\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.scwang90:refresh-footer-classics:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-4\f32dec6a4022a9a504ce4323317954bd\transformed\jetified-refresh-footer-classics-2.0.5\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.scwang90:refresh-footer-classics:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-4\f32dec6a4022a9a504ce4323317954bd\transformed\jetified-refresh-footer-classics-2.0.5\AndroidManifest.xml:7:5-9:41
MERGED from [com.soundcloud.android:android-crop:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\e14d147c3d78b264e53fe39d2725f464\transformed\jetified-android-crop-1.0.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.soundcloud.android:android-crop:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\e14d147c3d78b264e53fe39d2725f464\transformed\jetified-android-crop-1.0.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.getActivity:ShapeDrawable:3.2] C:\Users\<USER>\.gradle\caches\transforms-4\872814dde57cc26585340a57c712db13\transformed\jetified-ShapeDrawable-3.2\AndroidManifest.xml:7:5-44
MERGED from [com.github.getActivity:ShapeDrawable:3.2] C:\Users\<USER>\.gradle\caches\transforms-4\872814dde57cc26585340a57c712db13\transformed\jetified-ShapeDrawable-3.2\AndroidManifest.xml:7:5-44
MERGED from [io.github.scwang90:refresh-drawable-paint:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-4\1d78fa585898a65b2b2237096416fca7\transformed\jetified-refresh-drawable-paint-2.0.5\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.scwang90:refresh-drawable-paint:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-4\1d78fa585898a65b2b2237096416fca7\transformed\jetified-refresh-drawable-paint-2.0.5\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml
uses-permission#android.permission.VIBRATE
ADDED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:8:5-66
MERGED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:21:5-66
MERGED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:21:5-66
	android:name
		ADDED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:8:22-63
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:11:5-76
	android:name
		ADDED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:11:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:13:5-75
	android:name
		ADDED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:13:22-72
uses-permission#android.permission.CAMERA
ADDED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:14:5-65
MERGED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:11:5-65
MERGED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:11:5-65
	android:name
		ADDED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:14:22-62
queries
ADDED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:16:5-20:15
MERGED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:9:5-16:15
MERGED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:9:5-16:15
intent#action:name:android.intent.action.SEND
ADDED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:17:9-19:18
action#android.intent.action.SEND
ADDED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:18:13-65
	android:name
		ADDED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:18:21-62
activity#top.wangchenyan.common.ui.activity.FragmentContainerActivity
ADDED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:23:9-25:50
	android:screenOrientation
		ADDED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:25:13-47
	android:name
		ADDED from [com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:24:13-88
meta-data#android.notch_support
ADDED from [com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:11:9-13:36
	android:value
		ADDED from [com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:13:13-33
	android:name
		ADDED from [com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:12:13-49
activity#com.lxj.xpopup.util.XPermission$PermissionActivity
ADDED from [com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:15:9-17:75
	android:theme
		ADDED from [com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:17:13-72
	android:name
		ADDED from [com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:16:13-78
uses-feature#android.hardware.camera
ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:13:5-60
	android:name
		ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:13:19-57
uses-feature#android.hardware.camera.autofocus
ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:14:5-70
	android:name
		ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:14:19-67
uses-permission#android.permission.FLASHLIGHT
ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:16:5-69
	android:name
		ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:16:22-66
uses-permission#android.permission.MOUNT_UNMOUNT_FILESYSTEMS
ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:20:5-84
	android:name
		ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:20:22-81
provider#cn.bertsir.zbar.utils.QrFileProvider
ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:27:9-35:20
	android:grantUriPermissions
		ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:31:13-47
	android:authorities
		ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:29:13-69
	android:exported
		ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:28:13-64
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:32:13-34:57
	android:resource
		ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:34:17-54
	android:name
		ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:33:17-67
activity#cn.bertsir.zbar.QRActivity
ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:37:9-39:77
	android:configChanges
		ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:39:13-74
	android:name
		ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:38:13-54
activity#cn.bertsir.zbar.utils.PermissionUtils$PermissionActivity
ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:40:9-42:58
	android:theme
		ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:42:13-55
	android:name
		ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:41:13-84
activity#com.soundcloud.android.crop.CropImageActivity
ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:43:9-82
	android:name
		ADDED from [cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:43:19-79
intent#action:name:android.intent.action.MAIN
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:10:9-12:18
intent#action:name:android.intent.action.VIEW
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:13:9-15:18
activity#com.blankj.utilcode.util.UtilsTransActivity4MainProcess
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:19:9-24:75
	android:windowSoftInputMode
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:24:13-72
	android:exported
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:22:13-37
	android:configChanges
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:21:13-74
	android:theme
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:23:13-55
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:20:13-83
activity#com.blankj.utilcode.util.UtilsTransActivity
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:25:9-31:75
	android:multiprocess
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:29:13-40
	android:windowSoftInputMode
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:31:13-72
	android:exported
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:28:13-37
	android:configChanges
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:27:13-74
	android:theme
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:30:13-55
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:26:13-71
provider#com.blankj.utilcode.util.UtilsFileProvider
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:33:9-41:20
	android:grantUriPermissions
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:37:13-47
	android:authorities
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:35:13-73
	android:exported
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:36:13-37
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:34:13-70
service#com.blankj.utilcode.util.MessengerUtils$ServerService
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:43:9-49:19
	android:exported
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:45:13-37
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:44:13-81
intent-filter#action:name:${applicationId}.messenger
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:46:13-48:29
intent-filter#action:name:me.wcy.music.messenger
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:46:13-48:29
action#${applicationId}.messenger
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:17-69
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:25-66
action#me.wcy.music.messenger
ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:17-69
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:25-66
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\99ca1d9bdcab120c686037f192b4447f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\99ca1d9bdcab120c686037f192b4447f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#me.wcy.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#me.wcy.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
provider#me.wcy.router.RouterProvider
ADDED from [com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:8:9-12:43
	android:authorities
		ADDED from [com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:10:13-68
	android:multiprocess
		ADDED from [com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:12:13-40
	android:exported
		ADDED from [com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:9:13-56
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
provider#com.qw.soul.permission.InitProvider
ADDED from [com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:10:9-14:43
	android:authorities
		ADDED from [com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:12:13-71
	android:multiprocess
		ADDED from [com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:14:13-40
	android:exported
		ADDED from [com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:13:13-37
	android:name
		ADDED from [com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:11:13-63
service#com.liulishuo.filedownloader.services.FileDownloadService$SharedMainProcessService
ADDED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:12:9-118
	android:name
		ADDED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:12:18-115
service#com.liulishuo.filedownloader.services.FileDownloadService$SeparateProcessService
ADDED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:13:9-15:49
	android:process
		ADDED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:15:13-46
	android:name
		ADDED from [com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:14:13-108
