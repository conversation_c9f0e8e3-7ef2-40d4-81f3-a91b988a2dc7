package me.wcy.music.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import me.wcy.music.service.cache.AudioCacheManager;
import me.wcy.music.storage.db.MusicDatabase;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MusicRepository_Factory implements Factory<MusicRepository> {
  private final Provider<MusicDatabase> databaseProvider;

  private final Provider<AudioCacheManager> audioCacheManagerProvider;

  public MusicRepository_Factory(Provider<MusicDatabase> databaseProvider,
      Provider<AudioCacheManager> audioCacheManagerProvider) {
    this.databaseProvider = databaseProvider;
    this.audioCacheManagerProvider = audioCacheManagerProvider;
  }

  @Override
  public MusicRepository get() {
    return newInstance(databaseProvider.get(), audioCacheManagerProvider.get());
  }

  public static MusicRepository_Factory create(Provider<MusicDatabase> databaseProvider,
      Provider<AudioCacheManager> audioCacheManagerProvider) {
    return new MusicRepository_Factory(databaseProvider, audioCacheManagerProvider);
  }

  public static MusicRepository newInstance(MusicDatabase database,
      AudioCacheManager audioCacheManager) {
    return new MusicRepository(database, audioCacheManager);
  }
}
