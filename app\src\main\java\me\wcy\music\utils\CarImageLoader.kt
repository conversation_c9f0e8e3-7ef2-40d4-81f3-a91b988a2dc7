package me.wcy.music.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.widget.ImageView
import androidx.core.graphics.drawable.toBitmap
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestBuilder
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import me.wcy.music.R
import java.lang.ref.WeakReference

/**
 * 车载环境优化的图片加载器
 * 专为Android Automotive设计，优化内存使用和加载性能
 */
object CarImageLoader {
    
    // 车载环境的图片尺寸配置
    object CarImageSize {
        const val ALBUM_COVER_LARGE = 512  // 播放界面大封面
        const val ALBUM_COVER_MEDIUM = 256 // 列表中等封面
        const val ALBUM_COVER_SMALL = 128  // 播放栏小封面
        const val ALBUM_COVER_THUMB = 64   // 缩略图
    }
    
    // 内存缓存管理
    private val bitmapCache = mutableMapOf<String, WeakReference<Bitmap>>()
    private const val MAX_CACHE_SIZE = 50 // 最大缓存数量
    
    /**
     * 为车载环境优化的图片加载配置
     */
    private fun getCarOptimizedOptions(size: Int, corners: Int = 0): RequestOptions {
        return RequestOptions()
            .override(size, size)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .skipMemoryCache(false)
            .apply {
                if (corners > 0) {
                    transform(CenterCrop(), RoundedCorners(corners))
                } else {
                    centerCrop()
                }
            }
    }
    
    /**
     * 加载专辑封面 - 车载优化版本
     */
    fun ImageView.loadCarCover(
        url: Any?,
        size: Int = CarImageSize.ALBUM_COVER_MEDIUM,
        corners: Int = 0,
        onLoadComplete: ((Bitmap?) -> Unit)? = null
    ) {
        val cacheKey = "${url}_${size}_${corners}"
        
        // 先检查内存缓存
        bitmapCache[cacheKey]?.get()?.let { cachedBitmap ->
            setImageBitmap(cachedBitmap)
            onLoadComplete?.invoke(cachedBitmap)
            return
        }
        
        val requestBuilder = Glide.with(this)
            .asBitmap()
            .load(url)
            .apply(getCarOptimizedOptions(size, corners))
            .placeholder(R.drawable.ic_default_cover)
            .error(R.drawable.ic_default_cover)
        
        if (onLoadComplete != null) {
            requestBuilder.listener(object : RequestListener<Bitmap> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Bitmap>?,
                    isFirstResource: Boolean
                ): Boolean {
                    onLoadComplete(null)
                    return false
                }
                
                override fun onResourceReady(
                    resource: Bitmap?,
                    model: Any?,
                    target: Target<Bitmap>?,
                    dataSource: DataSource?,
                    isFirstResource: Boolean
                ): Boolean {
                    resource?.let { bitmap ->
                        // 缓存到内存
                        cacheBitmap(cacheKey, bitmap)
                        onLoadComplete(bitmap)
                    }
                    return false
                }
            })
        }
        
        requestBuilder.into(this)
    }
    
    /**
     * 预加载图片
     */
    suspend fun preloadImage(context: Context, url: String, size: Int = CarImageSize.ALBUM_COVER_MEDIUM) {
        withContext(Dispatchers.IO) {
            try {
                val bitmap = Glide.with(context)
                    .asBitmap()
                    .load(url)
                    .apply(getCarOptimizedOptions(size))
                    .submit()
                    .get()
                
                val cacheKey = "${url}_${size}_0"
                cacheBitmap(cacheKey, bitmap)
            } catch (e: Exception) {
                // 预加载失败不影响主流程
            }
        }
    }
    
    /**
     * 批量预加载图片
     */
    suspend fun preloadImages(context: Context, urls: List<String>, size: Int = CarImageSize.ALBUM_COVER_MEDIUM) {
        withContext(Dispatchers.IO) {
            urls.forEach { url ->
                try {
                    preloadImage(context, url, size)
                } catch (e: Exception) {
                    // 继续加载下一张
                }
            }
        }
    }
    
    /**
     * 缓存Bitmap到内存
     */
    private fun cacheBitmap(key: String, bitmap: Bitmap) {
        // 清理过期缓存
        if (bitmapCache.size >= MAX_CACHE_SIZE) {
            cleanupCache()
        }
        bitmapCache[key] = WeakReference(bitmap)
    }
    
    /**
     * 清理内存缓存
     */
    private fun cleanupCache() {
        val iterator = bitmapCache.iterator()
        var cleanedCount = 0
        while (iterator.hasNext() && cleanedCount < MAX_CACHE_SIZE / 2) {
            val entry = iterator.next()
            if (entry.value.get() == null) {
                iterator.remove()
                cleanedCount++
            }
        }
    }
    
    /**
     * 清空所有缓存
     */
    fun clearCache(context: Context) {
        bitmapCache.clear()
        Glide.get(context).clearMemory()
    }
    
    /**
     * 获取缓存大小信息
     */
    fun getCacheInfo(): String {
        val validCacheCount = bitmapCache.values.count { it.get() != null }
        return "缓存数量: $validCacheCount/${bitmapCache.size}"
    }
}

/**
 * 扩展函数 - 简化调用
 */
fun ImageView.loadCarAlbumCover(url: Any?, corners: Int = 0) {
    CarImageLoader.run {
        loadCarCover(url, CarImageLoader.CarImageSize.ALBUM_COVER_MEDIUM, corners)
    }
}

fun ImageView.loadCarAlbumCoverLarge(url: Any?, corners: Int = 0, onLoadComplete: ((Bitmap?) -> Unit)? = null) {
    CarImageLoader.run {
        loadCarCover(url, CarImageLoader.CarImageSize.ALBUM_COVER_LARGE, corners, onLoadComplete)
    }
}

fun ImageView.loadCarAlbumCoverSmall(url: Any?, corners: Int = 0) {
    CarImageLoader.run {
        loadCarCover(url, CarImageLoader.CarImageSize.ALBUM_COVER_SMALL, corners)
    }
}
