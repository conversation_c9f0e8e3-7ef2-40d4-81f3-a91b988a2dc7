<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/llActions"
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:orientation="horizontal"
        android:paddingHorizontal="32dp">

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <ImageView
            android:id="@+id/ivLike"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:paddingVertical="2dp"
            android:src="@drawable/ic_favorite_selector" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <ImageView
            android:id="@+id/ivDownload"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:paddingVertical="2dp"
            android:src="@drawable/ic_download"
            app:tint="@color/translucent_white_p80" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/llProgress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <TextView
            android:id="@+id/tvCurrentTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/play_time_start"
            android:textColor="@color/car_text_primary"
            android:textSize="@dimen/car_text_size_medium"
            android:minWidth="@dimen/car_min_touch_target" />

        <me.wcy.music.widget.CarProgressBar
            android:id="@+id/sbProgress"
            android:layout_width="0dp"
            android:layout_height="@dimen/car_min_touch_target"
            android:layout_weight="1"
            android:layout_marginHorizontal="@dimen/car_spacing_medium" />

        <TextView
            android:id="@+id/tvTotalTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/play_time_start"
            android:textColor="@color/car_text_primary"
            android:textSize="@dimen/car_text_size_medium"
            android:minWidth="@dimen/car_min_touch_target" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/car_extra_large_touch_target"
        android:layout_marginTop="@dimen/car_spacing_large"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/car_spacing_extra_large"
        android:gravity="center">

        <ImageView
            android:id="@+id/ivMode"
            android:layout_width="@dimen/car_large_touch_target"
            android:layout_height="@dimen/car_large_touch_target"
            android:layout_marginHorizontal="@dimen/car_spacing_small"
            android:padding="@dimen/car_spacing_small"
            android:src="@drawable/ic_play_mode_level_list"
            android:background="@drawable/car_button_selector"
            android:contentDescription="播放模式"
            android:hapticFeedbackEnabled="true"
            app:tint="@color/car_text_primary" />

        <ImageView
            android:id="@+id/ivPrev"
            android:layout_width="@dimen/car_large_touch_target"
            android:layout_height="@dimen/car_large_touch_target"
            android:layout_marginHorizontal="@dimen/car_spacing_small"
            android:padding="@dimen/car_spacing_medium"
            android:src="@drawable/ic_previous"
            android:background="@drawable/car_button_selector"
            android:contentDescription="上一首"
            android:hapticFeedbackEnabled="true"
            app:tint="@color/car_text_primary" />

        <FrameLayout
            android:id="@+id/flPlay"
            android:layout_width="@dimen/car_extra_large_touch_target"
            android:layout_height="@dimen/car_extra_large_touch_target"
            android:layout_marginHorizontal="@dimen/car_spacing_medium"
            android:background="@drawable/car_button_selector"
            android:hapticFeedbackEnabled="true">

            <ImageView
                android:id="@+id/ivPlay"
                android:layout_width="@dimen/car_min_touch_target"
                android:layout_height="@dimen/car_min_touch_target"
                android:layout_gravity="center"
                android:src="@drawable/ic_playing_play_pause_selector"
                android:contentDescription="播放/暂停"
                app:tint="@color/car_theme_primary" />

            <ProgressBar
                android:id="@+id/loadingProgress"
                android:layout_width="@dimen/car_min_touch_target"
                android:layout_height="@dimen/car_min_touch_target"
                android:layout_gravity="center"
                android:indeterminate="true"
                android:visibility="gone"
                android:indeterminateTint="@color/car_theme_primary" />
        </FrameLayout>

        <ImageView
            android:id="@+id/ivNext"
            android:layout_width="@dimen/car_large_touch_target"
            android:layout_height="@dimen/car_large_touch_target"
            android:layout_marginHorizontal="@dimen/car_spacing_small"
            android:padding="@dimen/car_spacing_medium"
            android:src="@drawable/ic_next"
            android:background="@drawable/car_button_selector"
            android:contentDescription="下一首"
            android:hapticFeedbackEnabled="true"
            app:tint="@color/car_text_primary" />

        <ImageView
            android:id="@+id/ivPlaylist"
            android:layout_width="@dimen/car_large_touch_target"
            android:layout_height="@dimen/car_large_touch_target"
            android:layout_marginHorizontal="@dimen/car_spacing_small"
            android:padding="@dimen/car_spacing_small"
            android:src="@drawable/ic_playlist"
            android:background="@drawable/car_button_selector"
            android:contentDescription="播放列表"
            android:hapticFeedbackEnabled="true"
            app:tint="@color/car_text_primary" />
    </LinearLayout>
</LinearLayout>