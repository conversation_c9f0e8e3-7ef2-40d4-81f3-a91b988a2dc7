<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/llActions"
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:orientation="horizontal"
        android:paddingHorizontal="32dp">

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <ImageView
            android:id="@+id/ivLike"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:paddingVertical="2dp"
            android:src="@drawable/ic_favorite_selector" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <ImageView
            android:id="@+id/ivDownload"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:paddingVertical="2dp"
            android:src="@drawable/ic_download"
            app:tint="@color/translucent_white_p80" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/llProgress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <TextView
            android:id="@+id/tvCurrentTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/play_time_start"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:minWidth="48dp" />

        <me.wcy.music.widget.CarProgressBar
            android:id="@+id/sbProgress"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="16dp" />

        <TextView
            android:id="@+id/tvTotalTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/play_time_start"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:minWidth="48dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:layout_marginTop="24dp"
        android:orientation="horizontal"
        android:paddingHorizontal="32dp"
        android:gravity="center">

        <ImageView
            android:id="@+id/ivMode"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginHorizontal="8dp"
            android:padding="8dp"
            android:src="@drawable/ic_play_mode_level_list"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="播放模式"
            android:hapticFeedbackEnabled="true"
            app:tint="@color/translucent_white_p80" />

        <ImageView
            android:id="@+id/ivPrev"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginHorizontal="8dp"
            android:padding="16dp"
            android:src="@drawable/ic_previous"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="上一首"
            android:hapticFeedbackEnabled="true"
            app:tint="@color/translucent_white_p80" />

        <FrameLayout
            android:id="@+id/flPlay"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginHorizontal="16dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:hapticFeedbackEnabled="true">

            <ImageView
                android:id="@+id/ivPlay"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_playing_play_pause_selector"
                android:contentDescription="播放/暂停"
                app:tint="@color/translucent_white_p80" />

            <ProgressBar
                android:id="@+id/loadingProgress"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="center"
                android:indeterminate="true"
                android:visibility="gone"
                android:indeterminateTint="@color/common_theme_color" />
        </FrameLayout>

        <ImageView
            android:id="@+id/ivNext"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginHorizontal="8dp"
            android:padding="16dp"
            android:src="@drawable/ic_next"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="下一首"
            android:hapticFeedbackEnabled="true"
            app:tint="@color/translucent_white_p80" />

        <ImageView
            android:id="@+id/ivPlaylist"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginHorizontal="8dp"
            android:padding="16dp"
            android:src="@drawable/ic_playlist"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="播放列表"
            android:hapticFeedbackEnabled="true"
            app:tint="@color/translucent_white_p80" />
    </LinearLayout>
</LinearLayout>