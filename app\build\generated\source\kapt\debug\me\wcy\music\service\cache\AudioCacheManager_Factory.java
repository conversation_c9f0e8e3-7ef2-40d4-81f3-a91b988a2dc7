package me.wcy.music.service.cache;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AudioCacheManager_Factory implements Factory<AudioCacheManager> {
  private final Provider<Context> contextProvider;

  public AudioCacheManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public AudioCacheManager get() {
    return newInstance(contextProvider.get());
  }

  public static AudioCacheManager_Factory create(Provider<Context> contextProvider) {
    return new AudioCacheManager_Factory(contextProvider);
  }

  public static AudioCacheManager newInstance(Context context) {
    return new AudioCacheManager(context);
  }
}
