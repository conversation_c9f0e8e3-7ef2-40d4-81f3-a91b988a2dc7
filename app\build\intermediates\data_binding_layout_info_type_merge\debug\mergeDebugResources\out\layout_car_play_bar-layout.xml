<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_car_play_bar" modulePackage="me.wcy.music" filePath="app\src\main\res\layout\layout_car_play_bar.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/layout_car_play_bar_0" view="LinearLayout"><Expressions/><location startLine="2" startOffset="0" endLine="137" endOffset="14"/></Target><Target id="@+id/ivCover" view="ImageView"><Expressions/><location startLine="19" startOffset="8" endLine="25" endOffset="71"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="36" startOffset="8" endLine="45" endOffset="40"/></Target><Target id="@+id/tvArtist" view="TextView"><Expressions/><location startLine="47" startOffset="8" endLine="56" endOffset="44"/></Target><Target id="@+id/ivPrev" view="ImageView"><Expressions/><location startLine="66" startOffset="8" endLine="75" endOffset="56"/></Target><Target id="@+id/flPlay" view="FrameLayout"><Expressions/><location startLine="77" startOffset="8" endLine="111" endOffset="21"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="85" startOffset="12" endLine="92" endOffset="72"/></Target><Target id="@+id/ivPlay" view="ImageView"><Expressions/><location startLine="94" startOffset="12" endLine="101" endOffset="60"/></Target><Target id="@+id/loadingProgress" view="ProgressBar"><Expressions/><location startLine="103" startOffset="12" endLine="110" endOffset="71"/></Target><Target id="@+id/ivNext" view="ImageView"><Expressions/><location startLine="113" startOffset="8" endLine="122" endOffset="56"/></Target><Target id="@+id/ivPlaylist" view="ImageView"><Expressions/><location startLine="124" startOffset="8" endLine="134" endOffset="56"/></Target></Targets></Layout>