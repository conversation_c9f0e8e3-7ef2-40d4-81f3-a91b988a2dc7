// Generated by view binder compiler. Do not edit!
package me.wcy.music.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import androidx.viewpager2.widget.ViewPager2;
import com.google.android.material.navigation.NavigationView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import me.wcy.music.R;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final NavigationView navigationView;

  @NonNull
  public final LinearLayout sideNavigation;

  @NonNull
  public final LinearLayout tabBar;

  @NonNull
  public final ViewPager2 viewPager;

  private ActivityMainBinding(@NonNull LinearLayout rootView,
      @NonNull NavigationView navigationView, @NonNull LinearLayout sideNavigation,
      @NonNull LinearLayout tabBar, @NonNull ViewPager2 viewPager) {
    this.rootView = rootView;
    this.navigationView = navigationView;
    this.sideNavigation = sideNavigation;
    this.tabBar = tabBar;
    this.viewPager = viewPager;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.navigationView;
      NavigationView navigationView = ViewBindings.findChildViewById(rootView, id);
      if (navigationView == null) {
        break missingId;
      }

      id = R.id.sideNavigation;
      LinearLayout sideNavigation = ViewBindings.findChildViewById(rootView, id);
      if (sideNavigation == null) {
        break missingId;
      }

      id = R.id.tabBar;
      LinearLayout tabBar = ViewBindings.findChildViewById(rootView, id);
      if (tabBar == null) {
        break missingId;
      }

      id = R.id.viewPager;
      ViewPager2 viewPager = ViewBindings.findChildViewById(rootView, id);
      if (viewPager == null) {
        break missingId;
      }

      return new ActivityMainBinding((LinearLayout) rootView, navigationView, sideNavigation,
          tabBar, viewPager);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
