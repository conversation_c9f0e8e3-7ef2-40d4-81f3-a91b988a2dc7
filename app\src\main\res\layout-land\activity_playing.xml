<?xml version="1.0" encoding="utf-8"?>
<!-- 车载横屏播放界面 -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/car_bg_primary">

    <FrameLayout
        android:id="@+id/flBackground"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/ivPlayingBg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:alpha="0.3"
            tools:src="@drawable/bg_playing_default" />

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/car_bg_primary"
            android:alpha="0.8" />
    </FrameLayout>

    <!-- 车载播放界面主内容 -->
    <LinearLayout
        android:id="@+id/llContent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:padding="20dp">

        <!-- 左侧专辑封面区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:orientation="vertical"
            android:gravity="center"
            android:paddingEnd="20dp">

            <!-- 关闭按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:layout_marginBottom="20dp">

                <include
                    android:id="@+id/titleLayout"
                    layout="@layout/activity_playing_title" />
            </LinearLayout>

            <!-- 专辑封面 -->
            <me.wcy.music.widget.AlbumCoverView
                android:id="@+id/albumCoverView"
                android:layout_width="280dp"
                android:layout_height="280dp"
                android:layout_gravity="center" />

        </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/lrcLayout"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <include
                    android:id="@+id/volumeLayout"
                    layout="@layout/activity_playing_volume"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintWidth_max="450dp" />

                <me.wcy.lrcview.LrcView
                    android:id="@+id/lrcView"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="16dp"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/volumeLayout"
                    app:layout_constraintWidth_max="450dp"
                    app:lrcAnimationDuration="1000"
                    app:lrcCurrentTextColor="@color/white"
                    app:lrcDividerHeight="24dp"
                    app:lrcNormalTextColor="@color/translucent_white_p50"
                    app:lrcPadding="40dp"
                    app:lrcTextSize="16dp"
                    app:lrcTimelineColor="@color/translucent_white_p50"
                    app:lrcTimelineTextColor="#CCFFFFFF" />

                <ImageView
                    android:id="@+id/ivLrcTopMask"
                    android:layout_width="match_parent"
                    android:layout_height="36dp"
                    app:layout_constraintTop_toTopOf="@+id/lrcView" />

                <ImageView
                    android:id="@+id/ivLrcBottomMask"
                    android:layout_width="match_parent"
                    android:layout_height="36dp"
                    app:layout_constraintBottom_toBottomOf="@+id/lrcView" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
    </LinearLayout>
</FrameLayout>