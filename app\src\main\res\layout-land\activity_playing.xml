<?xml version="1.0" encoding="utf-8"?>
<!-- 车载横屏播放界面 -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/car_bg_primary">

    <FrameLayout
        android:id="@+id/flBackground"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/ivPlayingBg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:alpha="0.3"
            tools:src="@drawable/bg_playing_default" />

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/car_bg_primary"
            android:alpha="0.8" />
    </FrameLayout>

    <!-- 车载播放界面主内容 -->
    <LinearLayout
        android:id="@+id/llContent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:padding="20dp">

        <!-- 左侧专辑封面区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:orientation="vertical"
            android:gravity="center"
            android:paddingEnd="20dp">

            <!-- 关闭按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:layout_marginBottom="20dp">

                <include
                    android:id="@+id/titleLayout"
                    layout="@layout/activity_playing_title" />
            </LinearLayout>

            <!-- 专辑封面 -->
            <me.wcy.music.widget.AlbumCoverView
                android:id="@+id/albumCoverView"
                android:layout_width="280dp"
                android:layout_height="280dp"
                android:layout_gravity="center" />

        </LinearLayout>

        <!-- 右侧播放控制和歌词区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:orientation="vertical"
            android:paddingStart="20dp">

            <!-- 歌曲信息和控制区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center_vertical">

                <!-- 歌曲标题和艺术家 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="30dp">

                    <TextView
                        android:id="@+id/tvSongTitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="歌曲标题"
                        android:textColor="@color/car_text_primary"
                        android:textSize="32sp"
                        android:textStyle="bold"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:layout_marginBottom="10dp" />

                    <TextView
                        android:id="@+id/tvArtist"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="艺术家"
                        android:textColor="@color/car_text_secondary"
                        android:textSize="20sp"
                        android:maxLines="1"
                        android:ellipsize="end" />
                </LinearLayout>

                <!-- 播放控制区域 -->
                <include
                    android:id="@+id/controlLayout"
                    layout="@layout/activity_playing_control"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <!-- 音量控制 -->
                <include
                    android:id="@+id/volumeLayout"
                    layout="@layout/activity_playing_volume"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp" />

            </LinearLayout>

            <!-- 歌词区域 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/lrcLayout"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:layout_marginTop="20dp">

                <me.wcy.lrcview.LrcView
                    android:id="@+id/lrcView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:lrcAnimationDuration="1000"
                    app:lrcCurrentTextColor="@color/car_theme_primary"
                    app:lrcDividerHeight="20dp"
                    app:lrcNormalTextColor="@color/car_text_secondary"
                    app:lrcPadding="20dp"
                    app:lrcTextSize="18sp"
                    app:lrcTimelineColor="@color/car_text_tertiary"
                    app:lrcTimelineTextColor="@color/car_text_secondary" />

                <ImageView
                    android:id="@+id/ivLrcTopMask"
                    android:layout_width="match_parent"
                    android:layout_height="36dp"
                    app:layout_constraintTop_toTopOf="@+id/lrcView" />

                <ImageView
                    android:id="@+id/ivLrcBottomMask"
                    android:layout_width="match_parent"
                    android:layout_height="36dp"
                    app:layout_constraintBottom_toBottomOf="@+id/lrcView" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

    </LinearLayout>
</FrameLayout>