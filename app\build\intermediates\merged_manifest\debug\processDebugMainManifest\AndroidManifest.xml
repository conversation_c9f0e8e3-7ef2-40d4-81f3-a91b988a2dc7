<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="me.wcy.music"
    android:versionCode="2030001"
    android:versionName="2.3.0-beta01" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="36" />

    <!-- 基础权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- Android Automotive 特性声明 -->
    <uses-feature
        android:name="android.hardware.type.automotive"
        android:required="false" />

    <!-- 车载音频应用特性 -->
    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />

    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.CAMERA" />

    <queries>
        <intent>
            <action android:name="android.intent.action.SEND" />
        </intent>
        <intent>
            <action android:name="android.intent.action.MAIN" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />
        </intent>
    </queries>

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />

    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />

    <permission
        android:name="me.wcy.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="me.wcy.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="me.wcy.music.MusicApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="true"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@drawable/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/AppTheme" >
        <service
            android:name="me.wcy.music.service.MusicService"
            android:exported="true"
            android:foregroundServiceType="mediaPlayback" >
            <intent-filter>
                <action android:name="androidx.media3.session.MediaSessionService" />
            </intent-filter>
        </service>

        <receiver
            android:name="me.wcy.music.download.DownloadReceiver"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.DOWNLOAD_COMPLETE" />
            </intent-filter>
        </receiver>

        <activity
            android:name="me.wcy.music.main.MainActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:screenOrientation="landscape" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <!-- Android Automotive 车载应用分类 -->
                <category android:name="android.intent.category.CAR_LAUNCHER" />
            </intent-filter>

            <!-- 媒体播放器意图过滤器 -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="audio/*" />
            </intent-filter>
        </activity>
        <activity android:name="me.wcy.music.common.MusicFragmentContainerActivity" />
        <activity
            android:name="me.wcy.music.main.SettingsActivity"
            android:label="@string/menu_setting" />
        <activity
            android:name="me.wcy.music.main.AboutActivity"
            android:label="@string/menu_about" />
        <activity
            android:name="me.wcy.music.main.playing.PlayingActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:launchMode="singleTop"
            android:screenOrientation="landscape"
            android:theme="@style/AppTheme.Popup" />
        <activity
            android:name="top.wangchenyan.common.ui.activity.FragmentContainerActivity"
            android:screenOrientation="behind" />

        <meta-data
            android:name="android.notch_support"
            android:value="true" /> <!-- PermissonActivity -->
        <activity
            android:name="com.lxj.xpopup.util.XPermission$PermissionActivity"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <provider
            android:name="cn.bertsir.zbar.utils.QrFileProvider"
            android:authorities="me.wcy.music.zbar.FileProvider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/qr_file_paths" />
        </provider>

        <activity
            android:name="cn.bertsir.zbar.QRActivity"
            android:configChanges="keyboardHidden|orientation|screenSize" />
        <activity
            android:name="cn.bertsir.zbar.utils.PermissionUtils$PermissionActivity"
            android:theme="@style/ActivityTranslucent" />
        <activity android:name="com.soundcloud.android.crop.CropImageActivity" />
        <activity
            android:name="com.blankj.utilcode.util.UtilsTransActivity4MainProcess"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:theme="@style/ActivityTranslucent"
            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
        <activity
            android:name="com.blankj.utilcode.util.UtilsTransActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:multiprocess="true"
            android:theme="@style/ActivityTranslucent"
            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />

        <provider
            android:name="com.blankj.utilcode.util.UtilsFileProvider"
            android:authorities="me.wcy.music.utilcode.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/util_code_provider_paths" />
        </provider>

        <service
            android:name="com.blankj.utilcode.util.MessengerUtils$ServerService"
            android:exported="false" >
            <intent-filter>
                <action android:name="me.wcy.music.messenger" />
            </intent-filter>
        </service>
        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="me.wcy.music.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <provider
            android:name="me.wcy.router.RouterProvider"
            android:authorities="me.wcy.music.crouter.provider"
            android:exported="false"
            android:multiprocess="true" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <provider
            android:name="com.qw.soul.permission.InitProvider"
            android:authorities="me.wcy.music.permission.provider"
            android:exported="false"
            android:multiprocess="true" />

        <service android:name="com.liulishuo.filedownloader.services.FileDownloadService$SharedMainProcessService" />
        <service
            android:name="com.liulishuo.filedownloader.services.FileDownloadService$SeparateProcessService"
            android:process=":filedownloader" />
    </application>

</manifest>