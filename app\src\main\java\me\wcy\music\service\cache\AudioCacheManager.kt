package me.wcy.music.service.cache

import android.content.Context
import androidx.media3.common.MediaItem
import androidx.media3.datasource.cache.Cache
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor
import androidx.media3.datasource.cache.SimpleCache
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import me.wcy.music.storage.preference.ConfigPreferences
import java.io.File
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 音频缓存管理器
 * 专为车载环境设计，支持渐进式下载和智能缓存策略
 */
@Singleton
class AudioCacheManager @Inject constructor(
    private val context: Context
) {
    
    companion object {
        private const val CACHE_DIR_NAME = "audio_cache"
        private const val DEFAULT_CACHE_SIZE = 500L * 1024 * 1024 // 500MB
        private const val MIN_CACHE_SIZE = 100L * 1024 * 1024 // 100MB
        private const val MAX_CACHE_SIZE = 2L * 1024 * 1024 * 1024 // 2GB
    }
    
    // 缓存实例
    private var _cache: Cache? = null
    val cache: Cache
        get() = _cache ?: initializeCache()
    
    // 缓存状态管理
    private val _cacheStates = ConcurrentHashMap<String, MutableStateFlow<CacheState>>()
    
    // 缓存配置
    private val cacheSize: Long
        get() = ConfigPreferences.audioCacheSize.takeIf { it > 0 } ?: DEFAULT_CACHE_SIZE
    
    /**
     * 缓存状态
     */
    data class CacheState(
        val mediaId: String,
        val totalBytes: Long = 0,
        val cachedBytes: Long = 0,
        val isFullyCached: Boolean = false,
        val downloadProgress: Float = 0f
    ) {
        val cachePercentage: Int
            get() = if (totalBytes > 0) ((cachedBytes * 100) / totalBytes).toInt() else 0
    }
    
    /**
     * 初始化缓存
     */
    private fun initializeCache(): Cache {
        val cacheDir = File(context.cacheDir, CACHE_DIR_NAME)
        val evictor = LeastRecentlyUsedCacheEvictor(cacheSize)
        
        return SimpleCache(cacheDir, evictor).also {
            _cache = it
        }
    }
    
    /**
     * 获取缓存状态
     */
    fun getCacheState(mediaId: String): StateFlow<CacheState> {
        return _cacheStates.getOrPut(mediaId) {
            MutableStateFlow(CacheState(mediaId))
        }.asStateFlow()
    }
    
    /**
     * 更新缓存状态
     */
    fun updateCacheState(mediaId: String, totalBytes: Long, cachedBytes: Long) {
        val stateFlow = _cacheStates.getOrPut(mediaId) {
            MutableStateFlow(CacheState(mediaId))
        }
        
        val isFullyCached = cachedBytes >= totalBytes && totalBytes > 0
        val downloadProgress = if (totalBytes > 0) cachedBytes.toFloat() / totalBytes else 0f
        
        stateFlow.value = CacheState(
            mediaId = mediaId,
            totalBytes = totalBytes,
            cachedBytes = cachedBytes,
            isFullyCached = isFullyCached,
            downloadProgress = downloadProgress
        )
    }
    
    /**
     * 检查是否已缓存
     */
    fun isFullyCached(mediaId: String): Boolean {
        return _cacheStates[mediaId]?.value?.isFullyCached ?: false
    }
    
    /**
     * 获取缓存进度
     */
    fun getCacheProgress(mediaId: String): Float {
        return _cacheStates[mediaId]?.value?.downloadProgress ?: 0f
    }
    
    /**
     * 预缓存音频文件
     */
    suspend fun precacheAudio(mediaItem: MediaItem, priority: Int = 0) {
        withContext(Dispatchers.IO) {
            try {
                // 这里可以实现预缓存逻辑
                // 例如：下载音频文件的前几秒或者整个文件
                val mediaId = mediaItem.mediaId
                updateCacheState(mediaId, 0, 0)
                
                // TODO: 实现实际的预缓存逻辑
                // 可以使用ExoPlayer的预加载功能
                
            } catch (e: Exception) {
                // 预缓存失败不影响播放
            }
        }
    }
    
    /**
     * 清理过期缓存
     */
    suspend fun cleanupExpiredCache() {
        withContext(Dispatchers.IO) {
            try {
                // 清理超过一定时间未访问的缓存
                val currentTime = System.currentTimeMillis()
                val expireTime = 7 * 24 * 60 * 60 * 1000L // 7天
                
                // TODO: 实现基于时间的缓存清理
                
            } catch (e: Exception) {
                // 清理失败不影响主流程
            }
        }
    }
    
    /**
     * 获取缓存大小信息
     */
    suspend fun getCacheSizeInfo(): CacheSizeInfo {
        return withContext(Dispatchers.IO) {
            try {
                val cacheDir = File(context.cacheDir, CACHE_DIR_NAME)
                val usedSize = calculateDirectorySize(cacheDir)
                val totalSize = cacheSize
                val freeSize = totalSize - usedSize
                
                CacheSizeInfo(
                    usedSize = usedSize,
                    totalSize = totalSize,
                    freeSize = freeSize,
                    usagePercentage = ((usedSize * 100) / totalSize).toInt()
                )
            } catch (e: Exception) {
                CacheSizeInfo(0, cacheSize, cacheSize, 0)
            }
        }
    }
    
    /**
     * 设置缓存大小
     */
    fun setCacheSize(sizeInMB: Long) {
        val sizeInBytes = sizeInMB * 1024 * 1024
        val clampedSize = sizeInBytes.coerceIn(MIN_CACHE_SIZE, MAX_CACHE_SIZE)
        ConfigPreferences.audioCacheSize = clampedSize
        
        // 重新初始化缓存
        _cache?.release()
        _cache = null
    }
    
    /**
     * 清空所有缓存
     */
    suspend fun clearAllCache() {
        withContext(Dispatchers.IO) {
            try {
                _cache?.release()
                val cacheDir = File(context.cacheDir, CACHE_DIR_NAME)
                cacheDir.deleteRecursively()
                _cache = null
                _cacheStates.clear()
            } catch (e: Exception) {
                // 清理失败
            }
        }
    }
    
    /**
     * 计算目录大小
     */
    private fun calculateDirectorySize(directory: File): Long {
        var size = 0L
        if (directory.exists() && directory.isDirectory) {
            directory.listFiles()?.forEach { file ->
                size += if (file.isDirectory) {
                    calculateDirectorySize(file)
                } else {
                    file.length()
                }
            }
        }
        return size
    }
    
    /**
     * 缓存大小信息
     */
    data class CacheSizeInfo(
        val usedSize: Long,
        val totalSize: Long,
        val freeSize: Long,
        val usagePercentage: Int
    ) {
        fun getUsedSizeMB(): Long = usedSize / (1024 * 1024)
        fun getTotalSizeMB(): Long = totalSize / (1024 * 1024)
        fun getFreeSizeMB(): Long = freeSize / (1024 * 1024)
    }
    
    /**
     * 释放资源
     */
    fun release() {
        _cache?.release()
        _cache = null
        _cacheStates.clear()
    }
}
