package me.wcy.music.utils

import android.content.Context
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.os.Build
import android.view.HapticFeedbackConstants
import android.view.TouchDelegate
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import me.wcy.music.R

/**
 * 车载触摸优化工具类
 * 专为Android Automotive设计，优化触摸交互体验
 */
object CarTouchUtils {

    /**
     * 为View设置最小触摸目标大小
     */
    fun View.setMinTouchTarget(minSize: Int = 48) {
        val minSizePx = (minSize * context.resources.displayMetrics.density).toInt()

        post {
            val parent = parent as? ViewGroup ?: return@post
            val rect = Rect()
            getHitRect(rect)

            // 计算需要扩展的区域
            val deltaX = maxOf(0, (minSizePx - rect.width()) / 2)
            val deltaY = maxOf(0, (minSizePx - rect.height()) / 2)

            if (deltaX > 0 || deltaY > 0) {
                rect.inset(-deltaX, -deltaY)
                parent.touchDelegate = TouchDelegate(rect, this)
            }
        }
    }

    /**
     * 为View添加车载优化的触觉反馈
     */
    fun View.addCarHapticFeedback(feedbackType: Int = HapticFeedbackConstants.VIRTUAL_KEY) {
        setOnClickListener { view ->
            // 执行触觉反馈
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                view.performHapticFeedback(HapticFeedbackConstants.CONFIRM)
            } else {
                view.performHapticFeedback(feedbackType)
            }

            // 执行原有的点击逻辑
            (tag as? View.OnClickListener)?.onClick(view)
        }
    }

    /**
     * 设置车载优化的点击监听器（包含触觉反馈）
     */
    fun View.setCarClickListener(listener: View.OnClickListener) {
        tag = listener
        setOnClickListener { view ->
            // 触觉反馈
            performCarHapticFeedback()
            // 执行点击逻辑
            listener.onClick(view)
        }
    }

    /**
     * 执行车载优化的触觉反馈
     */
    fun View.performCarHapticFeedback() {
        when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> {
                performHapticFeedback(HapticFeedbackConstants.CONFIRM)
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                performHapticFeedback(HapticFeedbackConstants.CONTEXT_CLICK)
            }
            else -> {
                performHapticFeedback(HapticFeedbackConstants.VIRTUAL_KEY)
            }
        }
    }

    /**
     * 为进度条设置扩展的触摸区域
     */
    fun View.setProgressBarTouchArea(extraPadding: Int = 24) {
        val extraPaddingPx = (extraPadding * context.resources.displayMetrics.density).toInt()

        post {
            val parent = parent as? ViewGroup ?: return@post
            val rect = Rect()
            getHitRect(rect)

            // 扩展触摸区域
            rect.inset(-extraPaddingPx, -extraPaddingPx)
            parent.touchDelegate = TouchDelegate(rect, this)
        }
    }

    /**
     * 优化列表项的触摸体验
     */
    fun View.optimizeListItemTouch() {
        // 设置最小触摸目标
        setMinTouchTarget(72)

        // 添加触摸状态变化效果
        background = ContextCompat.getDrawable(context, R.drawable.car_list_item_selector)

        // 设置触觉反馈
        isHapticFeedbackEnabled = true
    }

    /**
     * 为按钮设置车载优化
     */
    fun View.optimizeCarButton() {
        // 设置最小触摸目标
        setMinTouchTarget(48)

        // 启用触觉反馈
        isHapticFeedbackEnabled = true

        // 添加按压效果
        background = ContextCompat.getDrawable(context, R.drawable.car_button_selector)
    }

    /**
     * 检查View是否满足车载触摸要求
     */
    fun View.checkCarTouchCompliance(): Boolean {
        val minSizePx = (48 * context.resources.displayMetrics.density).toInt()
        return width >= minSizePx && height >= minSizePx
    }

    /**
     * 获取推荐的车载触摸目标大小
     */
    fun Context.getCarTouchTargetSize(type: TouchTargetType): Int {
        return when (type) {
            TouchTargetType.SMALL -> resources.getDimensionPixelSize(R.dimen.car_min_touch_target)
            TouchTargetType.MEDIUM -> resources.getDimensionPixelSize(R.dimen.car_large_touch_target)
            TouchTargetType.LARGE -> resources.getDimensionPixelSize(R.dimen.car_extra_large_touch_target)
        }
    }

    enum class TouchTargetType {
        SMALL,   // 48dp - 最小触摸目标
        MEDIUM,  // 56dp - 推荐触摸目标
        LARGE    // 64dp - 大触摸目标
    }
}
