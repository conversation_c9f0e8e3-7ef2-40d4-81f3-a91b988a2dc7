package me.wcy.music.service.cache;

import android.content.Context;
import androidx.media3.common.MediaItem;
import androidx.media3.datasource.cache.Cache;
import androidx.media3.datasource.cache.CacheDataSource;
import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor;
import androidx.media3.datasource.cache.SimpleCache;
import kotlinx.coroutines.Dispatchers;
import kotlinx.coroutines.flow.StateFlow;
import me.wcy.music.storage.preference.ConfigPreferences;
import java.io.File;
import java.util.concurrent.ConcurrentHashMap;
import javax.inject.Inject;
import javax.inject.Singleton;

/**
 * 音频缓存管理器
 * 专为车载环境设计，支持渐进式下载和智能缓存策略
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u000b\b\u0007\u0018\u0000 22\u00020\u0001:\u0003012B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\u0013\u001a\u00020\u00102\u0006\u0010\u0014\u001a\u00020\u0015H\u0002J\u000e\u0010\u0016\u001a\u00020\u0017H\u0086@\u00a2\u0006\u0002\u0010\u0018J\u000e\u0010\u0019\u001a\u00020\u0017H\u0086@\u00a2\u0006\u0002\u0010\u0018J\u000e\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\tJ\u000e\u0010\u001d\u001a\u00020\u001eH\u0086@\u00a2\u0006\u0002\u0010\u0018J\u0014\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u000b0 2\u0006\u0010\u001c\u001a\u00020\tJ\b\u0010!\u001a\u00020\u0006H\u0002J\u000e\u0010\"\u001a\u00020#2\u0006\u0010\u001c\u001a\u00020\tJ \u0010$\u001a\u00020\u00172\u0006\u0010%\u001a\u00020&2\b\b\u0002\u0010\'\u001a\u00020(H\u0086@\u00a2\u0006\u0002\u0010)J\u0006\u0010*\u001a\u00020\u0017J\u000e\u0010+\u001a\u00020\u00172\u0006\u0010,\u001a\u00020\u0010J\u001e\u0010-\u001a\u00020\u00172\u0006\u0010\u001c\u001a\u00020\t2\u0006\u0010.\u001a\u00020\u00102\u0006\u0010/\u001a\u00020\u0010R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R \u0010\u0007\u001a\u0014\u0012\u0004\u0012\u00020\t\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0011\u0010\f\u001a\u00020\u00068F\u00a2\u0006\u0006\u001a\u0004\b\r\u0010\u000eR\u0014\u0010\u000f\u001a\u00020\u00108BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0011\u0010\u0012R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00063"}, d2 = {"Lme/wcy/music/service/cache/AudioCacheManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "_cache", "Landroidx/media3/datasource/cache/Cache;", "_cacheStates", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lme/wcy/music/service/cache/AudioCacheManager$CacheState;", "cache", "getCache", "()Landroidx/media3/datasource/cache/Cache;", "cacheSize", "", "getCacheSize", "()J", "calculateDirectorySize", "directory", "Ljava/io/File;", "cleanupExpiredCache", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearAllCache", "getCacheProgress", "", "mediaId", "getCacheSizeInfo", "Lme/wcy/music/service/cache/AudioCacheManager$CacheSizeInfo;", "getCacheState", "Lkotlinx/coroutines/flow/StateFlow;", "initializeCache", "isFullyCached", "", "precacheAudio", "mediaItem", "Landroidx/media3/common/MediaItem;", "priority", "", "(Landroidx/media3/common/MediaItem;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "release", "setCacheSize", "sizeInMB", "updateCacheState", "totalBytes", "cachedBytes", "CacheSizeInfo", "CacheState", "Companion", "app_debug"})
public final class AudioCacheManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String CACHE_DIR_NAME = "audio_cache";
    private static final long DEFAULT_CACHE_SIZE = 524288000L;
    private static final long MIN_CACHE_SIZE = 104857600L;
    private static final long MAX_CACHE_SIZE = 2147483648L;
    @org.jetbrains.annotations.Nullable()
    private androidx.media3.datasource.cache.Cache _cache;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, kotlinx.coroutines.flow.MutableStateFlow<me.wcy.music.service.cache.AudioCacheManager.CacheState>> _cacheStates = null;
    @org.jetbrains.annotations.NotNull()
    public static final me.wcy.music.service.cache.AudioCacheManager.Companion Companion = null;
    
    @javax.inject.Inject()
    public AudioCacheManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.media3.datasource.cache.Cache getCache() {
        return null;
    }
    
    private final long getCacheSize() {
        return 0L;
    }
    
    /**
     * 初始化缓存
     */
    private final androidx.media3.datasource.cache.Cache initializeCache() {
        return null;
    }
    
    /**
     * 获取缓存状态
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<me.wcy.music.service.cache.AudioCacheManager.CacheState> getCacheState(@org.jetbrains.annotations.NotNull()
    java.lang.String mediaId) {
        return null;
    }
    
    /**
     * 更新缓存状态
     */
    public final void updateCacheState(@org.jetbrains.annotations.NotNull()
    java.lang.String mediaId, long totalBytes, long cachedBytes) {
    }
    
    /**
     * 检查是否已缓存
     */
    public final boolean isFullyCached(@org.jetbrains.annotations.NotNull()
    java.lang.String mediaId) {
        return false;
    }
    
    /**
     * 获取缓存进度
     */
    public final float getCacheProgress(@org.jetbrains.annotations.NotNull()
    java.lang.String mediaId) {
        return 0.0F;
    }
    
    /**
     * 预缓存音频文件
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object precacheAudio(@org.jetbrains.annotations.NotNull()
    androidx.media3.common.MediaItem mediaItem, int priority, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 清理过期缓存
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object cleanupExpiredCache(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 获取缓存大小信息
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCacheSizeInfo(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super me.wcy.music.service.cache.AudioCacheManager.CacheSizeInfo> $completion) {
        return null;
    }
    
    /**
     * 设置缓存大小
     */
    public final void setCacheSize(long sizeInMB) {
    }
    
    /**
     * 清空所有缓存
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearAllCache(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 计算目录大小
     */
    private final long calculateDirectorySize(java.io.File directory) {
        return 0L;
    }
    
    /**
     * 释放资源
     */
    public final void release() {
    }
    
    /**
     * 缓存大小信息
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0007H\u00c6\u0003J1\u0010\u0013\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0006\u0010\u0017\u001a\u00020\u0003J\u0006\u0010\u0018\u001a\u00020\u0003J\u0006\u0010\u0019\u001a\u00020\u0003J\t\u0010\u001a\u001a\u00020\u0007H\u00d6\u0001J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\nR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\n\u00a8\u0006\u001d"}, d2 = {"Lme/wcy/music/service/cache/AudioCacheManager$CacheSizeInfo;", "", "usedSize", "", "totalSize", "freeSize", "usagePercentage", "", "(JJJI)V", "getFreeSize", "()J", "getTotalSize", "getUsagePercentage", "()I", "getUsedSize", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "getFreeSizeMB", "getTotalSizeMB", "getUsedSizeMB", "hashCode", "toString", "", "app_debug"})
    public static final class CacheSizeInfo {
        private final long usedSize = 0L;
        private final long totalSize = 0L;
        private final long freeSize = 0L;
        private final int usagePercentage = 0;
        
        public CacheSizeInfo(long usedSize, long totalSize, long freeSize, int usagePercentage) {
            super();
        }
        
        public final long getUsedSize() {
            return 0L;
        }
        
        public final long getTotalSize() {
            return 0L;
        }
        
        public final long getFreeSize() {
            return 0L;
        }
        
        public final int getUsagePercentage() {
            return 0;
        }
        
        public final long getUsedSizeMB() {
            return 0L;
        }
        
        public final long getTotalSizeMB() {
            return 0L;
        }
        
        public final long getFreeSizeMB() {
            return 0L;
        }
        
        public final long component1() {
            return 0L;
        }
        
        public final long component2() {
            return 0L;
        }
        
        public final long component3() {
            return 0L;
        }
        
        public final int component4() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final me.wcy.music.service.cache.AudioCacheManager.CacheSizeInfo copy(long usedSize, long totalSize, long freeSize, int usagePercentage) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * 缓存状态
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0015\b\u0086\b\u0018\u00002\u00020\u0001B5\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\bH\u00c6\u0003J\t\u0010\u001c\u001a\u00020\nH\u00c6\u0003J;\u0010\u001d\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\nH\u00c6\u0001J\u0013\u0010\u001e\u001a\u00020\b2\b\u0010\u001f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010 \u001a\u00020\rH\u00d6\u0001J\t\u0010!\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\f\u001a\u00020\r8F\u00a2\u0006\u0006\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0011\u00a8\u0006\""}, d2 = {"Lme/wcy/music/service/cache/AudioCacheManager$CacheState;", "", "mediaId", "", "totalBytes", "", "cachedBytes", "isFullyCached", "", "downloadProgress", "", "(Ljava/lang/String;JJZF)V", "cachePercentage", "", "getCachePercentage", "()I", "getCachedBytes", "()J", "getDownloadProgress", "()F", "()Z", "getMediaId", "()Ljava/lang/String;", "getTotalBytes", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
    public static final class CacheState {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String mediaId = null;
        private final long totalBytes = 0L;
        private final long cachedBytes = 0L;
        private final boolean isFullyCached = false;
        private final float downloadProgress = 0.0F;
        
        public CacheState(@org.jetbrains.annotations.NotNull()
        java.lang.String mediaId, long totalBytes, long cachedBytes, boolean isFullyCached, float downloadProgress) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getMediaId() {
            return null;
        }
        
        public final long getTotalBytes() {
            return 0L;
        }
        
        public final long getCachedBytes() {
            return 0L;
        }
        
        public final boolean isFullyCached() {
            return false;
        }
        
        public final float getDownloadProgress() {
            return 0.0F;
        }
        
        public final int getCachePercentage() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        public final long component2() {
            return 0L;
        }
        
        public final long component3() {
            return 0L;
        }
        
        public final boolean component4() {
            return false;
        }
        
        public final float component5() {
            return 0.0F;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final me.wcy.music.service.cache.AudioCacheManager.CacheState copy(@org.jetbrains.annotations.NotNull()
        java.lang.String mediaId, long totalBytes, long cachedBytes, boolean isFullyCached, float downloadProgress) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lme/wcy/music/service/cache/AudioCacheManager$Companion;", "", "()V", "CACHE_DIR_NAME", "", "DEFAULT_CACHE_SIZE", "", "MAX_CACHE_SIZE", "MIN_CACHE_SIZE", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}