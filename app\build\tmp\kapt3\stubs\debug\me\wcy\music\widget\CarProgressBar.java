package me.wcy.music.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.HapticFeedbackConstants;
import android.view.MotionEvent;
import android.view.View;
import androidx.core.content.ContextCompat;
import me.wcy.music.R;

/**
 * 车载优化的进度条
 * 支持显示缓存进度，类似YouTube的灰色缓存条
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\b\u0018\u0000 @2\u00020\u0001:\u0002@AB%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0010\u0010-\u001a\u00020.2\u0006\u0010/\u001a\u000200H\u0014J\u0018\u00101\u001a\u00020.2\u0006\u00102\u001a\u00020\u00072\u0006\u00103\u001a\u00020\u0007H\u0014J\u0010\u00104\u001a\u00020\u00152\u0006\u00105\u001a\u000206H\u0016J\u0018\u00107\u001a\u00020.2\u0006\u00108\u001a\u00020\u00072\b\b\u0002\u00109\u001a\u00020:J\u0016\u0010;\u001a\u00020.2\u0006\u0010<\u001a\u00020\r2\u0006\u0010=\u001a\u00020\u0007J\u0010\u0010>\u001a\u00020.2\u0006\u0010?\u001a\u00020\rH\u0002R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R$\u0010\u000e\u001a\u00020\r2\u0006\u0010\f\u001a\u00020\r@FX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000f\u0010\u0010\"\u0004\b\u0011\u0010\u0012R\u000e\u0010\u0013\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R$\u0010\u0016\u001a\u00020\u00072\u0006\u0010\f\u001a\u00020\u0007@FX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0017\u0010\u0018\"\u0004\b\u0019\u0010\u001aR\u000e\u0010\u001b\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u001c\u001a\u0004\u0018\u00010\u001dX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001e\u0010\u001f\"\u0004\b \u0010!R$\u0010\"\u001a\u00020\u00072\u0006\u0010\f\u001a\u00020\u0007@FX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b#\u0010\u0018\"\u0004\b$\u0010\u001aR\u000e\u0010%\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R$\u0010(\u001a\u00020\u00072\u0006\u0010\f\u001a\u00020\u0007@FX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b)\u0010\u0018\"\u0004\b*\u0010\u001aR\u000e\u0010+\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010,\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006B"}, d2 = {"Lme/wcy/music/widget/CarProgressBar;", "Landroid/view/View;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "backgroundPaint", "Landroid/graphics/Paint;", "cachePaint", "value", "", "cacheProgress", "getCacheProgress", "()F", "setCacheProgress", "(F)V", "dragProgress", "isDragging", "", "max", "getMax", "()I", "setMax", "(I)V", "minTouchTargetSize", "onProgressChangeListener", "Lme/wcy/music/widget/CarProgressBar$OnProgressChangeListener;", "getOnProgressChangeListener", "()Lme/wcy/music/widget/CarProgressBar$OnProgressChangeListener;", "setOnProgressChangeListener", "(Lme/wcy/music/widget/CarProgressBar$OnProgressChangeListener;)V", "progress", "getProgress", "setProgress", "progressHeight", "progressPaint", "secondaryPaint", "secondaryProgress", "getSecondaryProgress", "setSecondaryProgress", "thumbPaint", "thumbRadius", "onDraw", "", "canvas", "Landroid/graphics/Canvas;", "onMeasure", "widthMeasureSpec", "heightMeasureSpec", "onTouchEvent", "event", "Landroid/view/MotionEvent;", "setProgressSmooth", "targetProgress", "duration", "", "updateCacheState", "cachePercentage", "bufferPercentage", "updateDragProgress", "x", "Companion", "OnProgressChangeListener", "app_debug"})
public final class CarProgressBar extends android.view.View {
    private static final float MIN_TOUCH_TARGET_SIZE = 48.0F;
    private int max = 100;
    private int progress = 0;
    private int secondaryProgress = 0;
    private float cacheProgress = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint backgroundPaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint cachePaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint secondaryPaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint progressPaint = null;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint thumbPaint = null;
    private final float progressHeight = 0.0F;
    private final float thumbRadius = 0.0F;
    private final float minTouchTargetSize = 0.0F;
    private boolean isDragging = false;
    private int dragProgress = 0;
    @org.jetbrains.annotations.Nullable()
    private me.wcy.music.widget.CarProgressBar.OnProgressChangeListener onProgressChangeListener;
    @org.jetbrains.annotations.NotNull()
    public static final me.wcy.music.widget.CarProgressBar.Companion Companion = null;
    
    @kotlin.jvm.JvmOverloads()
    public CarProgressBar(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    public final int getMax() {
        return 0;
    }
    
    public final void setMax(int value) {
    }
    
    public final int getProgress() {
        return 0;
    }
    
    public final void setProgress(int value) {
    }
    
    public final int getSecondaryProgress() {
        return 0;
    }
    
    public final void setSecondaryProgress(int value) {
    }
    
    public final float getCacheProgress() {
        return 0.0F;
    }
    
    public final void setCacheProgress(float value) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final me.wcy.music.widget.CarProgressBar.OnProgressChangeListener getOnProgressChangeListener() {
        return null;
    }
    
    public final void setOnProgressChangeListener(@org.jetbrains.annotations.Nullable()
    me.wcy.music.widget.CarProgressBar.OnProgressChangeListener p0) {
    }
    
    @java.lang.Override()
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
    }
    
    @java.lang.Override()
    protected void onDraw(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas canvas) {
    }
    
    @java.lang.Override()
    public boolean onTouchEvent(@org.jetbrains.annotations.NotNull()
    android.view.MotionEvent event) {
        return false;
    }
    
    private final void updateDragProgress(float x) {
    }
    
    /**
     * 设置进度（带动画）
     */
    public final void setProgressSmooth(int targetProgress, long duration) {
    }
    
    /**
     * 更新缓存状态
     */
    public final void updateCacheState(float cachePercentage, int bufferPercentage) {
    }
    
    @kotlin.jvm.JvmOverloads()
    public CarProgressBar(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads()
    public CarProgressBar(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lme/wcy/music/widget/CarProgressBar$Companion;", "", "()V", "MIN_TOUCH_TARGET_SIZE", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&J\b\u0010\b\u001a\u00020\u0003H&J\b\u0010\t\u001a\u00020\u0003H&\u00a8\u0006\n"}, d2 = {"Lme/wcy/music/widget/CarProgressBar$OnProgressChangeListener;", "", "onProgressChanged", "", "progress", "", "fromUser", "", "onStartTrackingTouch", "onStopTrackingTouch", "app_debug"})
    public static abstract interface OnProgressChangeListener {
        
        public abstract void onProgressChanged(int progress, boolean fromUser);
        
        public abstract void onStartTrackingTouch();
        
        public abstract void onStopTrackingTouch();
    }
}