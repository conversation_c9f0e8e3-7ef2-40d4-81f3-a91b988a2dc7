package me.wcy.music.service.cache;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CacheModule_ProvideAudioCacheManagerFactory implements Factory<AudioCacheManager> {
  private final Provider<Context> contextProvider;

  public CacheModule_ProvideAudioCacheManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public AudioCacheManager get() {
    return provideAudioCacheManager(contextProvider.get());
  }

  public static CacheModule_ProvideAudioCacheManagerFactory create(
      Provider<Context> contextProvider) {
    return new CacheModule_ProvideAudioCacheManagerFactory(contextProvider);
  }

  public static AudioCacheManager provideAudioCacheManager(Context context) {
    return Preconditions.checkNotNullFromProvides(CacheModule.INSTANCE.provideAudioCacheManager(context));
  }
}
