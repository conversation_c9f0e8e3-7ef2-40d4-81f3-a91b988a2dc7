// Generated by view binder compiler. Do not edit!
package me.wcy.music.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import me.wcy.music.R;
import me.wcy.music.widget.CarProgressBar;

public final class ActivityPlayingControlBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final FrameLayout flPlay;

  @NonNull
  public final ImageView ivDownload;

  @NonNull
  public final ImageView ivLike;

  @NonNull
  public final ImageView ivMode;

  @NonNull
  public final ImageView ivNext;

  @NonNull
  public final ImageView ivPlay;

  @NonNull
  public final ImageView ivPlaylist;

  @NonNull
  public final ImageView ivPrev;

  @NonNull
  public final LinearLayout llActions;

  @NonNull
  public final LinearLayout llProgress;

  @NonNull
  public final ProgressBar loadingProgress;

  @NonNull
  public final CarProgressBar sbProgress;

  @NonNull
  public final TextView tvCurrentTime;

  @NonNull
  public final TextView tvTotalTime;

  private ActivityPlayingControlBinding(@NonNull LinearLayout rootView, @NonNull FrameLayout flPlay,
      @NonNull ImageView ivDownload, @NonNull ImageView ivLike, @NonNull ImageView ivMode,
      @NonNull ImageView ivNext, @NonNull ImageView ivPlay, @NonNull ImageView ivPlaylist,
      @NonNull ImageView ivPrev, @NonNull LinearLayout llActions, @NonNull LinearLayout llProgress,
      @NonNull ProgressBar loadingProgress, @NonNull CarProgressBar sbProgress,
      @NonNull TextView tvCurrentTime, @NonNull TextView tvTotalTime) {
    this.rootView = rootView;
    this.flPlay = flPlay;
    this.ivDownload = ivDownload;
    this.ivLike = ivLike;
    this.ivMode = ivMode;
    this.ivNext = ivNext;
    this.ivPlay = ivPlay;
    this.ivPlaylist = ivPlaylist;
    this.ivPrev = ivPrev;
    this.llActions = llActions;
    this.llProgress = llProgress;
    this.loadingProgress = loadingProgress;
    this.sbProgress = sbProgress;
    this.tvCurrentTime = tvCurrentTime;
    this.tvTotalTime = tvTotalTime;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPlayingControlBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPlayingControlBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_playing_control, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPlayingControlBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.flPlay;
      FrameLayout flPlay = ViewBindings.findChildViewById(rootView, id);
      if (flPlay == null) {
        break missingId;
      }

      id = R.id.ivDownload;
      ImageView ivDownload = ViewBindings.findChildViewById(rootView, id);
      if (ivDownload == null) {
        break missingId;
      }

      id = R.id.ivLike;
      ImageView ivLike = ViewBindings.findChildViewById(rootView, id);
      if (ivLike == null) {
        break missingId;
      }

      id = R.id.ivMode;
      ImageView ivMode = ViewBindings.findChildViewById(rootView, id);
      if (ivMode == null) {
        break missingId;
      }

      id = R.id.ivNext;
      ImageView ivNext = ViewBindings.findChildViewById(rootView, id);
      if (ivNext == null) {
        break missingId;
      }

      id = R.id.ivPlay;
      ImageView ivPlay = ViewBindings.findChildViewById(rootView, id);
      if (ivPlay == null) {
        break missingId;
      }

      id = R.id.ivPlaylist;
      ImageView ivPlaylist = ViewBindings.findChildViewById(rootView, id);
      if (ivPlaylist == null) {
        break missingId;
      }

      id = R.id.ivPrev;
      ImageView ivPrev = ViewBindings.findChildViewById(rootView, id);
      if (ivPrev == null) {
        break missingId;
      }

      id = R.id.llActions;
      LinearLayout llActions = ViewBindings.findChildViewById(rootView, id);
      if (llActions == null) {
        break missingId;
      }

      id = R.id.llProgress;
      LinearLayout llProgress = ViewBindings.findChildViewById(rootView, id);
      if (llProgress == null) {
        break missingId;
      }

      id = R.id.loadingProgress;
      ProgressBar loadingProgress = ViewBindings.findChildViewById(rootView, id);
      if (loadingProgress == null) {
        break missingId;
      }

      id = R.id.sbProgress;
      CarProgressBar sbProgress = ViewBindings.findChildViewById(rootView, id);
      if (sbProgress == null) {
        break missingId;
      }

      id = R.id.tvCurrentTime;
      TextView tvCurrentTime = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentTime == null) {
        break missingId;
      }

      id = R.id.tvTotalTime;
      TextView tvTotalTime = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalTime == null) {
        break missingId;
      }

      return new ActivityPlayingControlBinding((LinearLayout) rootView, flPlay, ivDownload, ivLike,
          ivMode, ivNext, ivPlay, ivPlaylist, ivPrev, llActions, llProgress, loadingProgress,
          sbProgress, tvCurrentTime, tvTotalTime);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
