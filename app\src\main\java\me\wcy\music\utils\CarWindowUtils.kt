package me.wcy.music.utils

import android.app.Activity
import android.os.Build
import android.view.View
import android.view.Window
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.view.WindowManager
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat

/**
 * 车载窗口工具类
 * 提供现代化的全屏模式和窗口管理功能，兼容不同Android版本
 */
object CarWindowUtils {
    
    /**
     * 设置车载全屏沉浸式模式
     * 使用现代API替代弃用的systemUiVisibility
     */
    fun Activity.setupCarFullscreenMode() {
        // 启用边到边显示
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ 使用WindowInsetsController
            setupModernFullscreen()
        } else {
            // Android 10及以下使用传统方式
            setupLegacyFullscreen()
        }
        
        // 设置窗口标志
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        
        // 强制横屏显示
        requestedOrientation = android.content.pm.ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
    }
    
    /**
     * Android 11+ 现代全屏模式
     */
    private fun Activity.setupModernFullscreen() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val controller = window.insetsController
            controller?.let {
                // 隐藏状态栏和导航栏
                it.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
                
                // 设置沉浸式模式
                it.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            }
        }
    }
    
    /**
     * Android 10及以下传统全屏模式
     */
    @Suppress("DEPRECATION")
    private fun Activity.setupLegacyFullscreen() {
        window.decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                        or View.SYSTEM_UI_FLAG_FULLSCREEN
                        or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                )
        
        window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
    }
    
    /**
     * 显示系统栏
     */
    fun Activity.showSystemBars() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            window.insetsController?.show(
                WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars()
            )
        } else {
            @Suppress("DEPRECATION")
            window.decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                            or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                            or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    )
        }
    }
    
    /**
     * 隐藏系统栏
     */
    fun Activity.hideSystemBars() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            window.insetsController?.hide(
                WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars()
            )
        } else {
            setupLegacyFullscreen()
        }
    }
    
    /**
     * 设置状态栏文字颜色
     */
    fun Activity.setStatusBarTextColor(isDark: Boolean) {
        val controller = WindowInsetsControllerCompat(window, window.decorView)
        controller.isAppearanceLightStatusBars = isDark
    }
    
    /**
     * 设置导航栏按钮颜色
     */
    fun Activity.setNavigationBarButtonColor(isDark: Boolean) {
        val controller = WindowInsetsControllerCompat(window, window.decorView)
        controller.isAppearanceLightNavigationBars = isDark
    }
    
    /**
     * 设置窗口背景
     */
    fun Window.setCarBackground(colorRes: Int) {
        statusBarColor = context.getColor(colorRes)
        navigationBarColor = context.getColor(colorRes)
    }
    
    /**
     * 启用硬件加速
     */
    fun Activity.enableHardwareAcceleration() {
        window.setFlags(
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        )
    }
    
    /**
     * 设置屏幕亮度
     */
    fun Activity.setScreenBrightness(brightness: Float) {
        val layoutParams = window.attributes
        layoutParams.screenBrightness = brightness.coerceIn(0f, 1f)
        window.attributes = layoutParams
    }
    
    /**
     * 检查是否为全屏模式
     */
    fun Activity.isFullscreen(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val controller = window.insetsController
            val insets = window.decorView.rootWindowInsets
            insets?.isVisible(WindowInsets.Type.statusBars()) == false ||
                    insets?.isVisible(WindowInsets.Type.navigationBars()) == false
        } else {
            @Suppress("DEPRECATION")
            (window.decorView.systemUiVisibility and View.SYSTEM_UI_FLAG_FULLSCREEN) != 0
        }
    }
    
    /**
     * 设置车载优化的窗口属性
     */
    fun Activity.setupCarWindowAttributes() {
        window.apply {
            // 启用硬件加速
            setFlags(
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            )
            
            // 保持屏幕常亮
            addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            
            // 禁用截屏（可选，增强安全性）
            // addFlags(WindowManager.LayoutParams.FLAG_SECURE)
            
            // 设置窗口格式
            setFormat(android.graphics.PixelFormat.RGBA_8888)
        }
    }
    
    /**
     * 处理窗口焦点变化（车载环境优化）
     */
    fun Activity.handleCarWindowFocusChanged(hasFocus: Boolean) {
        if (hasFocus) {
            // 重新应用全屏模式
            hideSystemBars()
        }
    }
    
    /**
     * 获取安全区域内边距
     */
    fun Activity.getSafeAreaInsets(): WindowInsetsCompat? {
        return WindowInsetsCompat.toWindowInsetsCompat(
            window.decorView.rootWindowInsets,
            window.decorView
        )
    }
    
    /**
     * 应用车载主题窗口样式
     */
    fun Activity.applyCarThemeWindow() {
        window.apply {
            // 设置状态栏透明
            statusBarColor = android.graphics.Color.TRANSPARENT
            navigationBarColor = android.graphics.Color.TRANSPARENT
            
            // 设置窗口背景
            setBackgroundDrawableResource(android.R.color.black)
        }
    }
}
