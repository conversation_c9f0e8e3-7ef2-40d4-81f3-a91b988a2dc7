<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/car_bg_tertiary" />
        </shape>
    </item>
    
    <!-- 聚焦状态 -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/car_bg_secondary" />
            <stroke android:width="1dp" android:color="@color/car_theme_primary" />
        </shape>
    </item>
    
    <!-- 选中状态 -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/car_bg_secondary" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
</selector>
