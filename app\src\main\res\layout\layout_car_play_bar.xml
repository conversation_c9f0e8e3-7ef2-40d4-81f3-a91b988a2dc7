<?xml version="1.0" encoding="utf-8"?>
<!-- 车载播放控制栏 - 保持原始PonyMusic设计风格 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/play_bar_height"
    android:background="@color/play_bar_bg"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp"
    android:elevation="4dp">

    <!-- 专辑封面 - 原始设计风格 -->
    <FrameLayout
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginEnd="12dp"
        android:background="@drawable/common_bg_image_placeholder_round">

        <ImageView
            android:id="@+id/ivCover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@null"
            android:scaleType="centerCrop"
            android:src="@drawable/common_bg_image_placeholder_round" />
    </FrameLayout>

    <!-- 歌曲信息 - 原始设计风格 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_marginEnd="12dp">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="无音乐"
            android:textColor="@color/common_text_h1_color"
            android:textSize="16sp"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tvArtist"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:text=""
            android:textColor="@color/common_text_h2_color"
            android:textSize="14sp"
            android:layout_marginTop="2dp" />
    </LinearLayout>

    <!-- 播放控制按钮 - 原始设计风格但保留车载适配 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/ivPrev"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:contentDescription="上一首"
            android:padding="12dp"
            android:src="@drawable/ic_previous"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:hapticFeedbackEnabled="true"
            android:tint="@color/common_text_h1_color" />

        <FrameLayout
            android:id="@+id/flPlay"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginHorizontal="4dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:hapticFeedbackEnabled="true">

            <ProgressBar
                android:id="@+id/progressBar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:progress="75"
                android:progressTint="@color/common_theme_color"
                android:progressBackgroundTint="@color/common_divider" />

            <ImageView
                android:id="@+id/ivPlay"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_gravity="center"
                android:contentDescription="@null"
                android:src="@drawable/ic_play_bar_play_pause_selector"
                android:tint="@color/common_text_h1_color" />

            <ProgressBar
                android:id="@+id/loadingProgress"
                android:layout_width="26dp"
                android:layout_height="26dp"
                android:layout_gravity="center"
                android:indeterminate="true"
                android:visibility="gone"
                android:indeterminateTint="@color/common_theme_color" />
        </FrameLayout>

        <ImageView
            android:id="@+id/ivNext"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:contentDescription="下一首"
            android:padding="12dp"
            android:src="@drawable/ic_next"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:hapticFeedbackEnabled="true"
            android:tint="@color/common_text_h1_color" />

        <ImageView
            android:id="@+id/ivPlaylist"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="4dp"
            android:contentDescription="播放列表"
            android:padding="12dp"
            android:src="@drawable/ic_playlist"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:hapticFeedbackEnabled="true"
            android:tint="@color/common_text_h1_color" />
    </LinearLayout>

</LinearLayout>
