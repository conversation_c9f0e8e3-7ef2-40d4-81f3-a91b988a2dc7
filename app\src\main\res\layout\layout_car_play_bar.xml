<?xml version="1.0" encoding="utf-8"?>
<!-- 车载播放控制栏 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:background="@color/car_bg_secondary"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="20dp"
    android:elevation="8dp">

    <!-- 专辑封面 -->
    <FrameLayout
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginEnd="16dp"
        android:background="@color/car_bg_tertiary">

        <ImageView
            android:id="@+id/ivCover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@null"
            android:scaleType="centerCrop"
            android:src="@drawable/common_bg_image_placeholder_round" />
    </FrameLayout>

    <!-- 歌曲信息 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_marginEnd="16dp">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="无音乐"
            android:textColor="@color/car_text_primary"
            android:textSize="@dimen/car_text_size_large"
            android:textStyle="bold"
            android:layout_marginBottom="@dimen/car_spacing_small" />

        <TextView
            android:id="@+id/tvArtist"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:text=""
            android:textColor="@color/car_text_secondary"
            android:textSize="@dimen/car_text_size_medium" />
    </LinearLayout>

    <!-- 播放控制按钮 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- 上一首 -->
        <ImageView
            android:id="@+id/ivPrev"
            android:layout_width="@dimen/car_min_touch_target"
            android:layout_height="@dimen/car_min_touch_target"
            android:contentDescription="上一首"
            android:padding="12dp"
            android:src="@drawable/ic_previous"
            android:background="@drawable/car_button_selector"
            android:tint="@color/car_text_primary"
            android:hapticFeedbackEnabled="true" />

        <!-- 播放/暂停 -->
        <FrameLayout
            android:id="@+id/flPlay"
            android:layout_width="@dimen/car_large_touch_target"
            android:layout_height="@dimen/car_large_touch_target"
            android:layout_marginHorizontal="8dp"
            android:background="@drawable/car_button_selector"
            android:hapticFeedbackEnabled="true">

            <ProgressBar
                android:id="@+id/progressBar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:progress="75"
                android:progressTint="@color/car_theme_primary"
                android:progressBackgroundTint="@color/car_bg_tertiary" />

            <ImageView
                android:id="@+id/ivPlay"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:contentDescription="@null"
                android:src="@drawable/ic_play_bar_play_pause_selector"
                android:tint="@color/car_theme_primary" />

            <ProgressBar
                android:id="@+id/loadingProgress"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_gravity="center"
                android:indeterminate="true"
                android:visibility="gone"
                android:indeterminateTint="@color/car_theme_primary" />
        </FrameLayout>

        <!-- 下一首 -->
        <ImageView
            android:id="@+id/ivNext"
            android:layout_width="@dimen/car_min_touch_target"
            android:layout_height="@dimen/car_min_touch_target"
            android:contentDescription="下一首"
            android:padding="12dp"
            android:src="@drawable/ic_next"
            android:background="@drawable/car_button_selector"
            android:tint="@color/car_text_primary"
            android:hapticFeedbackEnabled="true" />

        <!-- 播放列表 -->
        <ImageView
            android:id="@+id/ivPlaylist"
            android:layout_width="@dimen/car_min_touch_target"
            android:layout_height="@dimen/car_min_touch_target"
            android:contentDescription="播放列表"
            android:padding="12dp"
            android:src="@drawable/ic_playlist"
            android:background="@drawable/car_button_selector"
            android:layout_marginStart="8dp"
            android:tint="@color/car_text_primary"
            android:hapticFeedbackEnabled="true" />
    </LinearLayout>

</LinearLayout>
