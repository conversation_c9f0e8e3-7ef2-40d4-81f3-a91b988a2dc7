package me.wcy.music.repository

import androidx.media3.common.MediaItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import me.wcy.music.common.bean.PlaylistData
import me.wcy.music.common.bean.SongData
import me.wcy.music.discover.DiscoverApi
import me.wcy.music.search.SearchApi
import me.wcy.music.service.cache.AudioCacheManager
import me.wcy.music.storage.db.MusicDatabase
import me.wcy.music.storage.db.entity.SongEntity
import me.wcy.music.utils.CarImageLoader
import me.wcy.music.utils.toMediaItem
import me.wcy.music.utils.toSongEntity
import top.wangchenyan.common.model.CommonResult
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 音乐数据仓库
 * 统一管理本地数据库、网络API和缓存
 */
@Singleton
class MusicRepository @Inject constructor(
    private val database: MusicDatabase,
    private val audioCacheManager: AudioCacheManager
) {

    // 使用现有的API实例
    private val discoverApi = DiscoverApi.get()
    private val searchApi = SearchApi.get()

    /**
     * 获取推荐歌单
     */
    fun getRecommendPlaylists(): Flow<CommonResult<List<PlaylistData>>> = flow {
        try {
            val result = discoverApi.getRecommendPlaylists()
            if (result.code == 200) {
                emit(CommonResult.success(result.playlists))
            } else {
                emit(CommonResult.fail(msg = "获取推荐歌单失败"))
            }
        } catch (e: Exception) {
            emit(CommonResult.fail(msg = e.message ?: "获取推荐歌单失败"))
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 获取歌单详情
     */
    fun getPlaylistDetail(id: Long): Flow<CommonResult<PlaylistData>> = flow {
        try {
            val result = discoverApi.getPlaylistDetail(id)
            if (result.code == 200) {
                emit(CommonResult.success(result.playlist))
            } else {
                emit(CommonResult.fail(msg = "获取歌单详情失败"))
            }
        } catch (e: Exception) {
            emit(CommonResult.fail(msg = e.message ?: "获取歌单详情失败"))
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 搜索音乐
     */
    fun searchMusic(
        keywords: String,
        type: Int = 1,
        limit: Int = 30,
        offset: Int = 0
    ): Flow<CommonResult<List<SongData>>> = flow {
        try {
            val result = searchApi.search(type, keywords, limit, offset)
            if (result.isSuccessWithData()) {
                val songs = result.getDataOrThrow().songs
                emit(CommonResult.success(songs))
            } else {
                emit(CommonResult.fail(msg = result.msg ?: "搜索失败"))
            }
        } catch (e: Exception) {
            emit(CommonResult.fail(msg = e.message ?: "搜索失败"))
        }
    }.flowOn(Dispatchers.IO)

    /**
     * 获取音乐URL
     */
    suspend fun getMusicUrl(id: Long, level: String = "standard"): CommonResult<String> {
        return withContext(Dispatchers.IO) {
            try {
                val result = discoverApi.getSongUrl(id, level)
                if (result.isSuccessWithData()) {
                    val urls = result.getDataOrThrow()
                    val url = urls.firstOrNull()?.url ?: ""
                    CommonResult.success(url)
                } else {
                    CommonResult.fail(msg = result.msg ?: "获取音乐URL失败")
                }
            } catch (e: Exception) {
                CommonResult.fail(msg = e.message ?: "获取音乐URL失败")
            }
        }
    }

    /**
     * 获取歌词
     */
    suspend fun getLyric(id: Long): CommonResult<String> {
        return withContext(Dispatchers.IO) {
            try {
                val result = discoverApi.getLrc(id)
                if (result.code == 200 && result.lrc.isValid()) {
                    CommonResult.success(result.lrc.lyric)
                } else {
                    CommonResult.fail(msg = "获取歌词失败")
                }
            } catch (e: Exception) {
                CommonResult.fail(msg = e.message ?: "获取歌词失败")
            }
        }
    }

    /**
     * 添加歌曲到播放列表
     */
    suspend fun addToPlaylist(song: MediaItem) {
        withContext(Dispatchers.IO) {
            database.playlistDao().insert(song.toSongEntity())
        }
    }

    /**
     * 获取本地播放列表
     */
    suspend fun getLocalPlaylist(): List<MediaItem> {
        return withContext(Dispatchers.IO) {
            database.playlistDao().queryAll().map { it.toMediaItem() }
        }
    }

    /**
     * 清空播放列表
     */
    suspend fun clearPlaylist() {
        withContext(Dispatchers.IO) {
            database.playlistDao().clear()
        }
    }

    /**
     * 删除播放列表中的歌曲
     */
    suspend fun removeFromPlaylist(song: MediaItem) {
        withContext(Dispatchers.IO) {
            database.playlistDao().delete(song.toSongEntity())
        }
    }

    /**
     * 获取缓存状态
     */
    fun getCacheState(mediaId: String): StateFlow<AudioCacheManager.CacheState> {
        return audioCacheManager.getCacheState(mediaId)
    }

    /**
     * 预缓存音频
     */
    suspend fun precacheAudio(mediaItem: MediaItem, priority: Int = 0) {
        audioCacheManager.precacheAudio(mediaItem, priority)
    }

    /**
     * 预加载图片
     */
    suspend fun preloadImages(context: android.content.Context, urls: List<String>) {
        CarImageLoader.preloadImages(context, urls)
    }

    /**
     * 获取缓存大小信息
     */
    suspend fun getCacheSizeInfo(): AudioCacheManager.CacheSizeInfo {
        return audioCacheManager.getCacheSizeInfo()
    }

    /**
     * 清理缓存
     */
    suspend fun clearCache() {
        audioCacheManager.clearAllCache()
    }

    /**
     * 设置缓存大小
     */
    fun setCacheSize(sizeInMB: Long) {
        audioCacheManager.setCacheSize(sizeInMB)
    }

    /**
     * 批量预加载推荐内容
     */
    suspend fun preloadRecommendedContent(context: android.content.Context) {
        withContext(Dispatchers.IO) {
            try {
                // 获取推荐歌单
                val playlistResult = discoverApi.getRecommendPlaylists()
                if (playlistResult.code == 200) {
                    val playlists = playlistResult.playlists

                    // 预加载封面图片
                    val coverUrls = playlists.mapNotNull { it.coverImgUrl.takeIf { url -> url.isNotEmpty() } }
                    CarImageLoader.preloadImages(context, coverUrls)

                    // 预加载第一个歌单的歌曲信息
                    playlists.firstOrNull()?.let { playlist ->
                        try {
                            val detailResult = discoverApi.getPlaylistDetail(playlist.id)
                            if (detailResult.code == 200) {
                                val detail = detailResult.playlist
                                val songCoverUrls = detail.songList.take(5).mapNotNull { it.al.picUrl.takeIf { url -> url.isNotEmpty() } }
                                CarImageLoader.preloadImages(context, songCoverUrls)
                            }
                        } catch (e: Exception) {
                            // 预加载歌单详情失败不影响主流程
                        }
                    }
                }
            } catch (e: Exception) {
                // 预加载失败不影响主流程
            }
        }
    }
}
