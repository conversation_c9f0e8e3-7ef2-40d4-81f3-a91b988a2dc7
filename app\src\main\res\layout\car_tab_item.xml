<?xml version="1.0" encoding="utf-8"?>
<!-- 车载垂直导航标签项布局 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="12dp"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    tools:layout_width="80dp">

    <ImageView
        android:id="@+id/ivIcon"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_marginBottom="8dp"
        app:tint="@color/car_nav_icon_selector"
        tools:src="@drawable/ic_tab_discover" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/car_nav_text_selector"
        android:textSize="12sp"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end"
        tools:text="发现" />

</LinearLayout>
