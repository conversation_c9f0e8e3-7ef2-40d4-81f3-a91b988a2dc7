package me.wcy.music.service;

import androidx.annotation.MainThread;
import androidx.annotation.OptIn;
import androidx.lifecycle.MutableLiveData;
import androidx.media3.common.MediaItem;
import androidx.media3.common.PlaybackException;
import androidx.media3.common.Player;
import androidx.media3.common.util.UnstableApi;
import kotlinx.coroutines.Dispatchers;
import kotlinx.coroutines.flow.StateFlow;
import me.wcy.music.service.cache.AudioCacheManager;
import me.wcy.music.storage.db.MusicDatabase;
import me.wcy.music.storage.preference.ConfigPreferences;

/**
 * Created by wang<PERSON><PERSON>.top on 2024/3/27.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000~\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0011\u0018\u00002\u00020\u00012\u00020\u0002B\u001d\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u00a2\u0006\u0002\u0010\tJ\u0010\u0010.\u001a\u00020/2\u0006\u00100\u001a\u00020\u000fH\u0017J\b\u00101\u001a\u00020/H\u0017J\u0010\u00102\u001a\u00020/2\u0006\u00100\u001a\u00020\u000fH\u0017J\b\u00103\u001a\u00020\fH\u0017J\u0016\u00104\u001a\b\u0012\u0004\u0012\u0002050\u001b2\u0006\u00106\u001a\u000207H\u0016J\b\u00108\u001a\u00020/H\u0017J\u0010\u00109\u001a\u00020/2\u0006\u00106\u001a\u000207H\u0017J\b\u0010:\u001a\u00020/H\u0017J\u001e\u0010;\u001a\u00020/2\u0006\u0010<\u001a\u00020\u000f2\u0006\u0010=\u001a\u00020\fH\u0096@\u00a2\u0006\u0002\u0010>J\b\u0010?\u001a\u00020/H\u0002J\b\u0010@\u001a\u00020/H\u0017J\u001e\u0010A\u001a\u00020/2\f\u0010B\u001a\b\u0012\u0004\u0012\u00020\u000f0\u00172\u0006\u00100\u001a\u00020\u000fH\u0017J\u0010\u0010C\u001a\u00020/2\u0006\u0010D\u001a\u00020\fH\u0017J\u0010\u0010E\u001a\u00020/2\u0006\u0010F\u001a\u00020\u0011H\u0017J\b\u0010G\u001a\u00020/H\u0017R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\r\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000f0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R(\u0010\u0016\u001a\u001c\u0012\u0018\u0012\u0016\u0012\u0004\u0012\u00020\u000f \u0018*\n\u0012\u0004\u0012\u00020\u000f\u0018\u00010\u00170\u00170\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\f0\u001bX\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0012\u0010\u001e\u001a\u00020\u001fX\u0096\u0005\u00a2\u0006\u0006\u001a\u0004\b \u0010!R\u001c\u0010\"\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000f0#X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00110\u001bX\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001dR\u001a\u0010(\u001a\b\u0012\u0004\u0012\u00020\u00130\u001bX\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\u001dR\u001a\u0010*\u001a\b\u0012\u0004\u0012\u00020\u00150\u001bX\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010\u001dR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R.\u0010,\u001a\u001c\u0012\u0018\u0012\u0016\u0012\u0004\u0012\u00020\u000f \u0018*\n\u0012\u0004\u0012\u00020\u000f\u0018\u00010\u00170\u00170#X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010%\u00a8\u0006H"}, d2 = {"Lme/wcy/music/service/PlayerControllerImpl;", "Lme/wcy/music/service/PlayerController;", "Lkotlinx/coroutines/CoroutineScope;", "player", "Landroidx/media3/common/Player;", "db", "Lme/wcy/music/storage/db/MusicDatabase;", "audioCacheManager", "Lme/wcy/music/service/cache/AudioCacheManager;", "(Landroidx/media3/common/Player;Lme/wcy/music/storage/db/MusicDatabase;Lme/wcy/music/service/cache/AudioCacheManager;)V", "_bufferingPercent", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_currentSong", "Landroidx/lifecycle/MutableLiveData;", "Landroidx/media3/common/MediaItem;", "_playMode", "Lme/wcy/music/service/PlayMode;", "_playProgress", "", "_playState", "Lme/wcy/music/service/PlayState;", "_playlist", "", "kotlin.jvm.PlatformType", "audioSessionId", "bufferingPercent", "Lkotlinx/coroutines/flow/StateFlow;", "getBufferingPercent", "()Lkotlinx/coroutines/flow/StateFlow;", "coroutineContext", "Lkotlin/coroutines/CoroutineContext;", "getCoroutineContext", "()Lkotlin/coroutines/CoroutineContext;", "currentSong", "Landroidx/lifecycle/LiveData;", "getCurrentSong", "()Landroidx/lifecycle/LiveData;", "playMode", "getPlayMode", "playProgress", "getPlayProgress", "playState", "getPlayState", "playlist", "getPlaylist", "addAndPlay", "", "song", "clearPlaylist", "delete", "getAudioSessionId", "getCacheState", "Lme/wcy/music/service/cache/AudioCacheManager$CacheState;", "mediaId", "", "next", "play", "playPause", "precacheAudio", "mediaItem", "priority", "(Landroidx/media3/common/MediaItem;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "precacheNextSongs", "prev", "replaceAll", "songList", "seekTo", "msec", "setPlayMode", "mode", "stop", "app_debug"})
public final class PlayerControllerImpl implements me.wcy.music.service.PlayerController, kotlinx.coroutines.CoroutineScope {
    @org.jetbrains.annotations.NotNull()
    private final androidx.media3.common.Player player = null;
    @org.jetbrains.annotations.NotNull()
    private final me.wcy.music.storage.db.MusicDatabase db = null;
    @org.jetbrains.annotations.NotNull()
    private final me.wcy.music.service.cache.AudioCacheManager audioCacheManager = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.util.List<androidx.media3.common.MediaItem>> _playlist = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.List<androidx.media3.common.MediaItem>> playlist = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<androidx.media3.common.MediaItem> _currentSong = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<androidx.media3.common.MediaItem> currentSong = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<me.wcy.music.service.PlayState> _playState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<me.wcy.music.service.PlayState> playState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Long> _playProgress = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Long> playProgress = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Integer> _bufferingPercent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> bufferingPercent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<me.wcy.music.service.PlayMode> _playMode = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<me.wcy.music.service.PlayMode> playMode = null;
    private int audioSessionId = 0;
    
    public PlayerControllerImpl(@org.jetbrains.annotations.NotNull()
    androidx.media3.common.Player player, @org.jetbrains.annotations.NotNull()
    me.wcy.music.storage.db.MusicDatabase db, @org.jetbrains.annotations.NotNull()
    me.wcy.music.service.cache.AudioCacheManager audioCacheManager) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public androidx.lifecycle.LiveData<java.util.List<androidx.media3.common.MediaItem>> getPlaylist() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public androidx.lifecycle.LiveData<androidx.media3.common.MediaItem> getCurrentSong() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.StateFlow<me.wcy.music.service.PlayState> getPlayState() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.StateFlow<java.lang.Long> getPlayProgress() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.StateFlow<java.lang.Integer> getBufferingPercent() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.StateFlow<me.wcy.music.service.PlayMode> getPlayMode() {
        return null;
    }
    
    @java.lang.Override()
    @androidx.annotation.MainThread()
    public void addAndPlay(@org.jetbrains.annotations.NotNull()
    androidx.media3.common.MediaItem song) {
    }
    
    @java.lang.Override()
    @androidx.annotation.MainThread()
    public void replaceAll(@org.jetbrains.annotations.NotNull()
    java.util.List<androidx.media3.common.MediaItem> songList, @org.jetbrains.annotations.NotNull()
    androidx.media3.common.MediaItem song) {
    }
    
    @java.lang.Override()
    @androidx.annotation.MainThread()
    public void play(@org.jetbrains.annotations.NotNull()
    java.lang.String mediaId) {
    }
    
    @java.lang.Override()
    @androidx.annotation.MainThread()
    public void delete(@org.jetbrains.annotations.NotNull()
    androidx.media3.common.MediaItem song) {
    }
    
    @java.lang.Override()
    @androidx.annotation.MainThread()
    public void clearPlaylist() {
    }
    
    @java.lang.Override()
    @androidx.annotation.MainThread()
    public void playPause() {
    }
    
    @java.lang.Override()
    @androidx.annotation.MainThread()
    public void next() {
    }
    
    @java.lang.Override()
    @androidx.annotation.MainThread()
    public void prev() {
    }
    
    @java.lang.Override()
    @androidx.annotation.MainThread()
    public void seekTo(int msec) {
    }
    
    @java.lang.Override()
    @androidx.annotation.MainThread()
    public int getAudioSessionId() {
        return 0;
    }
    
    @java.lang.Override()
    @androidx.annotation.MainThread()
    public void setPlayMode(@org.jetbrains.annotations.NotNull()
    me.wcy.music.service.PlayMode mode) {
    }
    
    @java.lang.Override()
    @androidx.annotation.MainThread()
    public void stop() {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.StateFlow<me.wcy.music.service.cache.AudioCacheManager.CacheState> getCacheState(@org.jetbrains.annotations.NotNull()
    java.lang.String mediaId) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object precacheAudio(@org.jetbrains.annotations.NotNull()
    androidx.media3.common.MediaItem mediaItem, int priority, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 智能预缓存下一首歌曲
     */
    private final void precacheNextSongs() {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlin.coroutines.CoroutineContext getCoroutineContext() {
        return null;
    }
}