package me.wcy.music.repository;

import androidx.media3.common.MediaItem;
import kotlinx.coroutines.Dispatchers;
import kotlinx.coroutines.flow.Flow;
import kotlinx.coroutines.flow.StateFlow;
import me.wcy.music.common.bean.PlaylistData;
import me.wcy.music.common.bean.SongData;
import me.wcy.music.discover.DiscoverApi;
import me.wcy.music.search.SearchApi;
import me.wcy.music.service.cache.AudioCacheManager;
import me.wcy.music.storage.db.MusicDatabase;
import me.wcy.music.storage.db.entity.SongEntity;
import me.wcy.music.utils.CarImageLoader;
import top.wangchenyan.common.model.CommonResult;
import javax.inject.Inject;
import javax.inject.Singleton;

/**
 * 音乐数据仓库
 * 统一管理本地数据库、网络API和缓存
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0080\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u000fJ\u000e\u0010\u0010\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\u0011J\u000e\u0010\u0012\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\u0011J\u000e\u0010\u0013\u001a\u00020\u0014H\u0086@\u00a2\u0006\u0002\u0010\u0011J\u0014\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00170\u00162\u0006\u0010\u0018\u001a\u00020\u0019J\u0014\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u000e0\u001bH\u0086@\u00a2\u0006\u0002\u0010\u0011J\u001c\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00190\u001d2\u0006\u0010\u001e\u001a\u00020\u001fH\u0086@\u00a2\u0006\u0002\u0010 J&\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00190\u001d2\u0006\u0010\u001e\u001a\u00020\u001f2\b\b\u0002\u0010\"\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u0010#J\u001a\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020&0\u001d0%2\u0006\u0010\u001e\u001a\u00020\u001fJ\u0018\u0010\'\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020&0\u001b0\u001d0%J \u0010(\u001a\u00020\f2\u0006\u0010)\u001a\u00020\u000e2\b\b\u0002\u0010*\u001a\u00020+H\u0086@\u00a2\u0006\u0002\u0010,J$\u0010-\u001a\u00020\f2\u0006\u0010.\u001a\u00020/2\f\u00100\u001a\b\u0012\u0004\u0012\u00020\u00190\u001bH\u0086@\u00a2\u0006\u0002\u00101J\u0016\u00102\u001a\u00020\f2\u0006\u0010.\u001a\u00020/H\u0086@\u00a2\u0006\u0002\u00103J\u0016\u00104\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u000fJ>\u00105\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002060\u001b0\u001d0%2\u0006\u00107\u001a\u00020\u00192\b\b\u0002\u00108\u001a\u00020+2\b\b\u0002\u00109\u001a\u00020+2\b\b\u0002\u0010:\u001a\u00020+J\u000e\u0010;\u001a\u00020\f2\u0006\u0010<\u001a\u00020\u001fR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006="}, d2 = {"Lme/wcy/music/repository/MusicRepository;", "", "database", "Lme/wcy/music/storage/db/MusicDatabase;", "audioCacheManager", "Lme/wcy/music/service/cache/AudioCacheManager;", "(Lme/wcy/music/storage/db/MusicDatabase;Lme/wcy/music/service/cache/AudioCacheManager;)V", "discoverApi", "Lme/wcy/music/discover/DiscoverApi;", "searchApi", "Lme/wcy/music/search/SearchApi;", "addToPlaylist", "", "song", "Landroidx/media3/common/MediaItem;", "(Landroidx/media3/common/MediaItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearCache", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearPlaylist", "getCacheSizeInfo", "Lme/wcy/music/service/cache/AudioCacheManager$CacheSizeInfo;", "getCacheState", "Lkotlinx/coroutines/flow/StateFlow;", "Lme/wcy/music/service/cache/AudioCacheManager$CacheState;", "mediaId", "", "getLocalPlaylist", "", "getLyric", "Ltop/wangchenyan/common/model/CommonResult;", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getMusicUrl", "level", "(JLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPlaylistDetail", "Lkotlinx/coroutines/flow/Flow;", "Lme/wcy/music/common/bean/PlaylistData;", "getRecommendPlaylists", "precacheAudio", "mediaItem", "priority", "", "(Landroidx/media3/common/MediaItem;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "preloadImages", "context", "Landroid/content/Context;", "urls", "(Landroid/content/Context;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "preloadRecommendedContent", "(Landroid/content/Context;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "removeFromPlaylist", "searchMusic", "Lme/wcy/music/common/bean/SongData;", "keywords", "type", "limit", "offset", "setCacheSize", "sizeInMB", "app_debug"})
public final class MusicRepository {
    @org.jetbrains.annotations.NotNull()
    private final me.wcy.music.storage.db.MusicDatabase database = null;
    @org.jetbrains.annotations.NotNull()
    private final me.wcy.music.service.cache.AudioCacheManager audioCacheManager = null;
    @org.jetbrains.annotations.NotNull()
    private final me.wcy.music.discover.DiscoverApi discoverApi = null;
    @org.jetbrains.annotations.NotNull()
    private final me.wcy.music.search.SearchApi searchApi = null;
    
    @javax.inject.Inject()
    public MusicRepository(@org.jetbrains.annotations.NotNull()
    me.wcy.music.storage.db.MusicDatabase database, @org.jetbrains.annotations.NotNull()
    me.wcy.music.service.cache.AudioCacheManager audioCacheManager) {
        super();
    }
    
    /**
     * 获取推荐歌单
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<top.wangchenyan.common.model.CommonResult<java.util.List<me.wcy.music.common.bean.PlaylistData>>> getRecommendPlaylists() {
        return null;
    }
    
    /**
     * 获取歌单详情
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<top.wangchenyan.common.model.CommonResult<me.wcy.music.common.bean.PlaylistData>> getPlaylistDetail(long id) {
        return null;
    }
    
    /**
     * 搜索音乐
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<top.wangchenyan.common.model.CommonResult<java.util.List<me.wcy.music.common.bean.SongData>>> searchMusic(@org.jetbrains.annotations.NotNull()
    java.lang.String keywords, int type, int limit, int offset) {
        return null;
    }
    
    /**
     * 获取音乐URL
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getMusicUrl(long id, @org.jetbrains.annotations.NotNull()
    java.lang.String level, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super top.wangchenyan.common.model.CommonResult<java.lang.String>> $completion) {
        return null;
    }
    
    /**
     * 获取歌词
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getLyric(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super top.wangchenyan.common.model.CommonResult<java.lang.String>> $completion) {
        return null;
    }
    
    /**
     * 添加歌曲到播放列表
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object addToPlaylist(@org.jetbrains.annotations.NotNull()
    androidx.media3.common.MediaItem song, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 获取本地播放列表
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getLocalPlaylist(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<androidx.media3.common.MediaItem>> $completion) {
        return null;
    }
    
    /**
     * 清空播放列表
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearPlaylist(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 删除播放列表中的歌曲
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object removeFromPlaylist(@org.jetbrains.annotations.NotNull()
    androidx.media3.common.MediaItem song, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 获取缓存状态
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<me.wcy.music.service.cache.AudioCacheManager.CacheState> getCacheState(@org.jetbrains.annotations.NotNull()
    java.lang.String mediaId) {
        return null;
    }
    
    /**
     * 预缓存音频
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object precacheAudio(@org.jetbrains.annotations.NotNull()
    androidx.media3.common.MediaItem mediaItem, int priority, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 预加载图片
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object preloadImages(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> urls, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 获取缓存大小信息
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCacheSizeInfo(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super me.wcy.music.service.cache.AudioCacheManager.CacheSizeInfo> $completion) {
        return null;
    }
    
    /**
     * 清理缓存
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearCache(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 设置缓存大小
     */
    public final void setCacheSize(long sizeInMB) {
    }
    
    /**
     * 批量预加载推荐内容
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object preloadRecommendedContent(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}