<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 基础权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- Android Automotive 特性声明 -->
    <uses-feature
        android:name="android.hardware.type.automotive"
        android:required="false" />

    <!-- 车载音频应用特性 -->
    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />

    <application
        android:name=".MusicApplication"
        android:allowBackup="true"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@drawable/ic_launcher_round"
        android:theme="@style/AppTheme">

        <service
            android:name=".service.MusicService"
            android:exported="true"
            android:foregroundServiceType="mediaPlayback">
            <intent-filter>
                <action android:name="androidx.media3.session.MediaSessionService" />
            </intent-filter>
        </service>

        <receiver
            android:name=".download.DownloadReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.DOWNLOAD_COMPLETE" />
            </intent-filter>
        </receiver>

        <activity
            android:name=".main.MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:screenOrientation="landscape"
            android:configChanges="orientation|screenSize|keyboardHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <!-- Android Automotive 车载应用分类 -->
                <category android:name="android.intent.category.CAR_LAUNCHER" />
            </intent-filter>

            <!-- 媒体播放器意图过滤器 -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="audio/*" />
            </intent-filter>
        </activity>
        <activity
            android:name=".common.MusicFragmentContainerActivity"
            android:screenOrientation="landscape"
            android:configChanges="orientation|screenSize|keyboardHidden" />
        <activity
            android:name=".main.SettingsActivity"
            android:label="@string/menu_setting"
            android:screenOrientation="landscape"
            android:configChanges="orientation|screenSize|keyboardHidden" />
        <activity
            android:name=".main.AboutActivity"
            android:label="@string/menu_about"
            android:screenOrientation="landscape"
            android:configChanges="orientation|screenSize|keyboardHidden" />
        <activity
            android:name="me.wcy.music.main.playing.PlayingActivity"
            android:launchMode="singleTop"
            android:screenOrientation="landscape"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:theme="@style/AppTheme.Popup" />
    </application>

</manifest>