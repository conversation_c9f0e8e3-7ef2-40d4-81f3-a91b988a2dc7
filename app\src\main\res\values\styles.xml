<resources>

    <style name="AppBaseTheme" parent="Theme.AppCompat.DayNight.NoActionBar" />

    <style name="AppTheme" parent="AppBaseTheme">
        <item name="colorPrimary">@color/common_theme_color</item>
        <item name="colorPrimaryDark">@color/common_theme_color</item>
        <item name="colorAccent">@color/common_theme_color</item>
        <!-- 车载全屏模式 -->
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>

    <style name="AppTheme.Popup" parent="AppTheme">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@style/AnimationPopup</item>
    </style>

    <style name="AnimationPopup" mce_bogus="1" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/anim_slide_up</item>
        <item name="android:activityOpenExitAnimation">@anim/anim_slide_down</item>
        <item name="android:activityCloseEnterAnimation">@anim/anim_slide_up</item>
        <item name="android:activityCloseExitAnimation">@anim/anim_slide_down</item>
    </style>

    <style name="TabLayout">
        <item name="tabIndicator">@drawable/ic_tab_layout_indicator</item>
        <item name="tabMode">fixed</item>
        <item name="tabPaddingEnd">0dp</item>
        <item name="tabPaddingStart">0dp</item>
        <item name="tabRippleColor">@null</item>
    </style>

    <style name="BottomSheetDialogTheme" parent="Theme.Material3.DayNight.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyleTransparent</item>
    </style>

    <style name="BottomSheetStyleTransparent" parent="Widget.Material3.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style>
</resources>
