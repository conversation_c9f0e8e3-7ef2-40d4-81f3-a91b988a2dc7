<?xml version="1.0" encoding="utf-8"?>
<!-- 车载横屏主界面布局 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:background="@color/common_bg">

    <!-- 左侧导航栏 -->
    <LinearLayout
        android:id="@+id/sideNavigation"
        android:layout_width="80dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/side_nav_bg"
        android:gravity="center_horizontal"
        android:paddingTop="20dp"
        android:paddingBottom="20dp">

        <!-- 导航图标容器 -->
        <LinearLayout
            android:id="@+id/tabBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal" />

    </LinearLayout>

    <!-- 主内容区域 -->
    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">

        <!-- ViewPager2 用于显示不同页面 -->
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <!-- 底部播放控制栏 -->
        <me.wcy.music.widget.PlayBar
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom" />

    </FrameLayout>

    <!-- 隐藏的抽屉导航（保持兼容性） -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/navigationView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:itemIconTint="?attr/colorPrimary"
        app:menu="@menu/navigation_menu" />

</LinearLayout>
