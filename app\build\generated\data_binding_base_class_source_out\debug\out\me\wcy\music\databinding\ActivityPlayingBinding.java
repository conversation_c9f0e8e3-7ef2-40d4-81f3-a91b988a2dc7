// Generated by view binder compiler. Do not edit!
package me.wcy.music.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import me.wcy.lrcview.LrcView;
import me.wcy.music.R;
import me.wcy.music.widget.AlbumCoverView;

public final class ActivityPlayingBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final AlbumCoverView albumCoverView;

  @NonNull
  public final ActivityPlayingControlBinding controlLayout;

  @NonNull
  public final FrameLayout flBackground;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout-land/</li>
   * </ul>
   */
  @Nullable
  public final ConstraintLayout flCoverLrc;

  @NonNull
  public final ImageView ivLrcBottomMask;

  @NonNull
  public final ImageView ivLrcTopMask;

  @NonNull
  public final ImageView ivPlayingBg;

  @NonNull
  public final View llContent;

  @NonNull
  public final View lrcLayout;

  @NonNull
  public final LrcView lrcView;

  @NonNull
  public final ActivityPlayingTitleBinding titleLayout;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-land/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final TextView tvArtist;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-land/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final TextView tvSongTitle;

  @NonNull
  public final ActivityPlayingVolumeBinding volumeLayout;

  private ActivityPlayingBinding(@NonNull FrameLayout rootView,
      @NonNull AlbumCoverView albumCoverView, @NonNull ActivityPlayingControlBinding controlLayout,
      @NonNull FrameLayout flBackground, @Nullable ConstraintLayout flCoverLrc,
      @NonNull ImageView ivLrcBottomMask, @NonNull ImageView ivLrcTopMask,
      @NonNull ImageView ivPlayingBg, @NonNull View llContent, @NonNull View lrcLayout,
      @NonNull LrcView lrcView, @NonNull ActivityPlayingTitleBinding titleLayout,
      @Nullable TextView tvArtist, @Nullable TextView tvSongTitle,
      @NonNull ActivityPlayingVolumeBinding volumeLayout) {
    this.rootView = rootView;
    this.albumCoverView = albumCoverView;
    this.controlLayout = controlLayout;
    this.flBackground = flBackground;
    this.flCoverLrc = flCoverLrc;
    this.ivLrcBottomMask = ivLrcBottomMask;
    this.ivLrcTopMask = ivLrcTopMask;
    this.ivPlayingBg = ivPlayingBg;
    this.llContent = llContent;
    this.lrcLayout = lrcLayout;
    this.lrcView = lrcView;
    this.titleLayout = titleLayout;
    this.tvArtist = tvArtist;
    this.tvSongTitle = tvSongTitle;
    this.volumeLayout = volumeLayout;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPlayingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPlayingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_playing, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPlayingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.albumCoverView;
      AlbumCoverView albumCoverView = ViewBindings.findChildViewById(rootView, id);
      if (albumCoverView == null) {
        break missingId;
      }

      id = R.id.controlLayout;
      View controlLayout = ViewBindings.findChildViewById(rootView, id);
      if (controlLayout == null) {
        break missingId;
      }
      ActivityPlayingControlBinding binding_controlLayout = ActivityPlayingControlBinding.bind(controlLayout);

      id = R.id.flBackground;
      FrameLayout flBackground = ViewBindings.findChildViewById(rootView, id);
      if (flBackground == null) {
        break missingId;
      }

      id = R.id.flCoverLrc;
      ConstraintLayout flCoverLrc = ViewBindings.findChildViewById(rootView, id);

      id = R.id.ivLrcBottomMask;
      ImageView ivLrcBottomMask = ViewBindings.findChildViewById(rootView, id);
      if (ivLrcBottomMask == null) {
        break missingId;
      }

      id = R.id.ivLrcTopMask;
      ImageView ivLrcTopMask = ViewBindings.findChildViewById(rootView, id);
      if (ivLrcTopMask == null) {
        break missingId;
      }

      id = R.id.ivPlayingBg;
      ImageView ivPlayingBg = ViewBindings.findChildViewById(rootView, id);
      if (ivPlayingBg == null) {
        break missingId;
      }

      id = R.id.llContent;
      View llContent = ViewBindings.findChildViewById(rootView, id);
      if (llContent == null) {
        break missingId;
      }

      id = R.id.lrcLayout;
      View lrcLayout = ViewBindings.findChildViewById(rootView, id);
      if (lrcLayout == null) {
        break missingId;
      }

      id = R.id.lrcView;
      LrcView lrcView = ViewBindings.findChildViewById(rootView, id);
      if (lrcView == null) {
        break missingId;
      }

      id = R.id.titleLayout;
      View titleLayout = ViewBindings.findChildViewById(rootView, id);
      if (titleLayout == null) {
        break missingId;
      }
      ActivityPlayingTitleBinding binding_titleLayout = ActivityPlayingTitleBinding.bind(titleLayout);

      id = R.id.tvArtist;
      TextView tvArtist = ViewBindings.findChildViewById(rootView, id);

      id = R.id.tvSongTitle;
      TextView tvSongTitle = ViewBindings.findChildViewById(rootView, id);

      id = R.id.volumeLayout;
      View volumeLayout = ViewBindings.findChildViewById(rootView, id);
      if (volumeLayout == null) {
        break missingId;
      }
      ActivityPlayingVolumeBinding binding_volumeLayout = ActivityPlayingVolumeBinding.bind(volumeLayout);

      return new ActivityPlayingBinding((FrameLayout) rootView, albumCoverView,
          binding_controlLayout, flBackground, flCoverLrc, ivLrcBottomMask, ivLrcTopMask,
          ivPlayingBg, llContent, lrcLayout, lrcView, binding_titleLayout, tvArtist, tvSongTitle,
          binding_volumeLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
