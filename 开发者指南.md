# PonyMusic Android Automotive 车载版本开发指南

## 项目概述

PonyMusic 已成功适配为 Android Automotive 横屏车载版本，采用 MVVM 架构，专为车载环境优化设计。

## 🎯 已完成功能

### 阶段一：基础架构和Android Automotive适配 ✅

#### 1. Android Automotive兼容性配置
- **AndroidManifest.xml 更新**：
  - 添加 `android.hardware.type.automotive` 特性声明
  - 添加 `android.software.leanback` 车载音频应用特性
  - 配置 `android.intent.category.CAR_LAUNCHER` 车载应用分类
  - 强制横屏显示：`android:screenOrientation="landscape"`

#### 2. 全屏沉浸式体验
- **MainActivity 和 PlayingActivity**：
  - 实现 `setupFullscreenMode()` 方法
  - 隐藏状态栏和导航栏
  - 设置全屏模式和屏幕常亮
  - 强制横屏显示

#### 3. API域名默认配置
- **ConfigPreferences.kt**：
  - 默认API域名设置为：`https://ncm.zhenxin.me`

### 阶段二：横屏UI布局适配 ✅

#### 1. 主界面横屏重构
- **activity_main.xml**：
  - 从 DrawerLayout 改为 LinearLayout 横向布局
  - 左侧80dp宽度垂直导航栏
  - 右侧主内容区域
  - 底部车载播放控制栏

#### 2. 车载导航系统
- **car_tab_item.xml**：新的垂直导航标签布局
- **car_nav_icon_selector.xml** 和 **car_nav_text_selector.xml**：导航状态选择器
- **MainActivity.kt**：适配新的车载标签系统

#### 3. 播放界面横屏优化
- **activity_playing.xml (横屏版)**：
  - 左侧：专辑封面区域（280x280dp）
  - 右侧：歌曲信息、播放控制、歌词显示
  - 车载主题颜色适配

#### 4. 车载播放控制栏
- **CarPlayBar.kt**：新的车载播放控制组件
- **layout_car_play_bar.xml**：80dp高度的车载播放栏
- 包含：专辑封面、歌曲信息、播放控制按钮

## 🎨 车载主题设计

### 颜色系统
```xml
<!-- 车载主题颜色 -->
<color name="car_theme_primary">#3498db</color>      <!-- 主题蓝色 -->
<color name="car_theme_secondary">#2c3e50</color>    <!-- 次要色 -->
<color name="car_bg_primary">#111111</color>         <!-- 主背景 -->
<color name="car_bg_secondary">#1a1a1a</color>       <!-- 次背景 -->
<color name="car_bg_tertiary">#222222</color>        <!-- 三级背景 -->
<color name="car_text_primary">#ffffff</color>       <!-- 主文字 -->
<color name="car_text_secondary">#999999</color>     <!-- 次文字 -->
<color name="car_text_tertiary">#666666</color>      <!-- 三级文字 -->
```

### 布局特点
- **横屏优先**：所有界面针对横屏车载屏幕优化
- **大触摸目标**：按钮最小48dp，适合驾驶时操作
- **高对比度**：深色背景配合高对比度文字
- **简洁导航**：左侧垂直导航栏，图标+文字

## 🏗️ 架构说明

### MVVM架构
- **Model**：数据层，包括网络API和本地存储
- **View**：UI层，Activity和Fragment
- **ViewModel**：业务逻辑层，管理UI状态

### 关键组件
1. **MainActivity**：主界面，管理导航和页面切换
2. **PlayingActivity**：播放界面，横屏优化
3. **CarPlayBar**：车载播放控制栏
4. **CustomTabPager**：页面管理器

## 📱 界面结构

### 主界面 (MainActivity)
```
┌─────────────────────────────────────┐
│ [导航] │        主内容区域          │
│ [图标] │                           │
│ [图标] │     ViewPager2            │
│ [图标] │                           │
│ [图标] │                           │
│        │                           │
│        └─────────────────────────────│
│              CarPlayBar             │
└─────────────────────────────────────┘
```

### 播放界面 (PlayingActivity)
```
┌─────────────────────────────────────┐
│ [关闭]  │                          │
│         │    歌曲标题               │
│ [专辑]  │    艺术家                 │
│ [封面]  │                          │
│         │    播放控制               │
│         │                          │
│         │    歌词显示               │
└─────────────────────────────────────┘
```

## 🔧 技术实现

### 全屏模式实现
```kotlin
private fun setupFullscreenMode() {
    window.decorView.systemUiVisibility = (
        View.SYSTEM_UI_FLAG_LAYOUT_STABLE
        or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
        or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
        or View.SYSTEM_UI_FLAG_FULLSCREEN
        or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
    )
    window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
    window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
}
```

### 车载导航实现
- 使用 `CarTabItemBinding` 替代原有的 `TabItemBinding`
- 垂直布局的导航图标，36dp大小适合车载操作
- 状态选择器支持选中/未选中状态

## 📋 下一步计划

### 阶段三：MVVM架构完善
1. **Repository模式实现**
   - 完善数据层抽象
   - 统一数据访问接口
   - 优化缓存策略

2. **ViewModel优化**
   - 完善现有ViewModel
   - 添加缺失的ViewModel
   - 优化数据流管理

### 阶段四：车载特性优化
1. **大屏触摸优化**
   - 增大触摸目标
   - 优化手势操作
   - 改进视觉反馈

2. **驾驶安全考虑**
   - 简化操作流程
   - 优化信息展示
   - 减少驾驶干扰

## 🚀 编译和运行

### 编译命令
```bash
./gradlew assembleDebug --console=plain --no-daemon
```

### 运行要求
- Android API 21+
- 支持横屏显示
- 推荐在Android Automotive设备或模拟器上测试

## 📝 更新日志

### v1.0.0 (2023-12-20)
- ✅ 完成Android Automotive基础适配
- ✅ 实现横屏UI布局
- ✅ 添加车载主题和颜色系统
- ✅ 创建车载专用播放控制栏
- ✅ 优化播放界面横屏显示
- ✅ 设置API域名默认值

### 阶段三：MVVM架构完善和性能优化 ✅

#### 1. Repository模式实现
- **MusicRepository.kt**：统一数据访问层
  - 整合DiscoverApi和SearchApi
  - 实现数据缓存策略
  - 统一错误处理和重试机制
  - 支持Flow响应式数据流

#### 2. 音频缓存系统
- **AudioCacheManager.kt**：专业音频缓存管理
  - 支持渐进式下载和缓存
  - 智能缓存策略（500MB默认，可配置）
  - 缓存状态实时监控
  - 支持预缓存和断点续传

#### 3. 图片加载优化
- **CarImageLoader.kt**：车载环境优化的图片加载器
  - 多尺寸图片缓存（大、中、小、缩略图）
  - 内存缓存管理（最大50张）
  - 预加载机制
  - Glide集成优化

#### 4. 进度条交互优化
- **CarProgressBar.kt**：车载专用进度条
  - 显示缓存进度（类似YouTube灰色缓存条）
  - 智能跳转，保持目标位置等待缓存
  - 48dp最小触摸目标
  - 流畅的拖拽体验

#### 5. 依赖注入优化
- **CacheModule.kt**：缓存相关依赖注入
- **PlayServiceModule.kt**：集成AudioCacheManager
- 完善Hilt依赖注入架构

### 🔧 **性能优化技术细节**

#### **图片加载策略**
```kotlin
// 车载环境图片尺寸配置
object CarImageSize {
    const val ALBUM_COVER_LARGE = 512   // 播放界面大封面
    const val ALBUM_COVER_MEDIUM = 256  // 列表中等封面
    const val ALBUM_COVER_SMALL = 128   // 播放栏小封面
    const val ALBUM_COVER_THUMB = 64    // 缩略图
}
```

#### **音频缓存配置**
```kotlin
// 缓存大小配置
private const val DEFAULT_CACHE_SIZE = 500L * 1024 * 1024 // 500MB
private const val MIN_CACHE_SIZE = 100L * 1024 * 1024     // 100MB
private const val MAX_CACHE_SIZE = 2L * 1024 * 1024 * 1024 // 2GB
```

#### **智能预缓存**
- 自动预缓存下一首歌曲
- 预加载推荐内容的封面图片
- 基于用户行为的智能预测

### 已知问题
- 部分弃用API警告（不影响功能，已在计划中升级）
- ExoPlayer缓存集成待完善
- 需要添加更多车载特性

### 下一步计划
1. **阶段四：车载特性优化**
   - 触摸交互优化（48dp最小目标）
   - 大屏显示优化
   - 动画和流畅性优化
   - 解决弃用API警告

2. **性能监控**
   - 添加内存使用监控
   - 网络请求性能监控
   - 图片加载性能优化

---

**开发团队**：PonyMusic Android Automotive Team
**最后更新**：2023年12月20日
