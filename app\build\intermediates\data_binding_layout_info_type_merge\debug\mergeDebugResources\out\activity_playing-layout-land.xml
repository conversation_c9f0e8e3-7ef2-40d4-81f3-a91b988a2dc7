<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_playing" modulePackage="me.wcy.music" filePath="app\src\main\res\layout-land\activity_playing.xml" directory="layout-land" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout-land/activity_playing_0" view="FrameLayout"><Expressions/><location startLine="2" startOffset="0" endLine="172" endOffset="13"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="47" startOffset="12" endLine="56" endOffset="26"/></Target><Target id="@+id/titleLayout" tag="binding_1" include="activity_playing_title"><Expressions/><location startLine="53" startOffset="16" endLine="55" endOffset="61"/></Target><Target tag="binding_2" view="LinearLayout"><Expressions/><location startLine="76" startOffset="12" endLine="128" endOffset="26"/></Target><Target id="@+id/controlLayout" tag="binding_2" include="activity_playing_control"><Expressions/><location startLine="114" startOffset="16" endLine="118" endOffset="58"/></Target><Target id="@+id/volumeLayout" tag="binding_2" include="activity_playing_volume"><Expressions/><location startLine="121" startOffset="16" endLine="126" endOffset="53"/></Target><Target id="@+id/flBackground" view="FrameLayout"><Expressions/><location startLine="9" startOffset="4" endLine="27" endOffset="17"/></Target><Target id="@+id/ivPlayingBg" view="ImageView"><Expressions/><location startLine="14" startOffset="8" endLine="20" endOffset="54"/></Target><Target id="@+id/llContent" view="LinearLayout"><Expressions/><location startLine="30" startOffset="4" endLine="171" endOffset="18"/></Target><Target id="@+id/albumCoverView" view="me.wcy.music.widget.AlbumCoverView"><Expressions/><location startLine="59" startOffset="12" endLine="63" endOffset="49"/></Target><Target id="@+id/tvSongTitle" view="TextView"><Expressions/><location startLine="90" startOffset="20" endLine="100" endOffset="60"/></Target><Target id="@+id/tvArtist" view="TextView"><Expressions/><location startLine="102" startOffset="20" endLine="110" endOffset="49"/></Target><Target id="@+id/lrcLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="131" startOffset="12" endLine="167" endOffset="63"/></Target><Target id="@+id/lrcView" view="me.wcy.lrcview.LrcView"><Expressions/><location startLine="138" startOffset="16" endLine="153" endOffset="74"/></Target><Target id="@+id/ivLrcTopMask" view="ImageView"><Expressions/><location startLine="155" startOffset="16" endLine="159" endOffset="69"/></Target><Target id="@+id/ivLrcBottomMask" view="ImageView"><Expressions/><location startLine="161" startOffset="16" endLine="165" endOffset="75"/></Target></Targets></Layout>